Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.5.2.0
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Huskui.Gallery", "src\Huskui.Gallery\Huskui.Gallery.csproj", "{50639FD1-2DA8-8D2E-C180-9133D440F1B5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Huskui.Avalonia", "src\Huskui.Avalonia\Huskui.Avalonia.csproj", "{0F51285B-777A-4ABD-F3B4-2C161F0C1045}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Polymerium.Trident", "src\Polymerium.Trident\Polymerium.Trident.csproj", "{20066E35-2BA1-1A18-00EC-7202CE90BFF4}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Trident.Abstractions", "src\Trident.Abstractions\Trident.Abstractions.csproj", "{4262B0E3-2620-1190-390E-3861ECE85572}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Huskui.Avalonia.Markdown", "src\Huskui.Avalonia.Markdown\Huskui.Avalonia.Markdown.csproj", "{28DBA674-86D1-9884-5D64-E9665788CAA3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Polymerium.App", "src\Polymerium.App\Polymerium.App.csproj", "{727DFD98-0E53-094E-8D4B-6BCEF082A6E3}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{50639FD1-2DA8-8D2E-C180-9133D440F1B5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{50639FD1-2DA8-8D2E-C180-9133D440F1B5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{50639FD1-2DA8-8D2E-C180-9133D440F1B5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{50639FD1-2DA8-8D2E-C180-9133D440F1B5}.Release|Any CPU.Build.0 = Release|Any CPU
		{0F51285B-777A-4ABD-F3B4-2C161F0C1045}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{0F51285B-777A-4ABD-F3B4-2C161F0C1045}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{0F51285B-777A-4ABD-F3B4-2C161F0C1045}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{0F51285B-777A-4ABD-F3B4-2C161F0C1045}.Release|Any CPU.Build.0 = Release|Any CPU
		{20066E35-2BA1-1A18-00EC-7202CE90BFF4}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{20066E35-2BA1-1A18-00EC-7202CE90BFF4}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{20066E35-2BA1-1A18-00EC-7202CE90BFF4}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{20066E35-2BA1-1A18-00EC-7202CE90BFF4}.Release|Any CPU.Build.0 = Release|Any CPU
		{4262B0E3-2620-1190-390E-3861ECE85572}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4262B0E3-2620-1190-390E-3861ECE85572}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4262B0E3-2620-1190-390E-3861ECE85572}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4262B0E3-2620-1190-390E-3861ECE85572}.Release|Any CPU.Build.0 = Release|Any CPU
		{28DBA674-86D1-9884-5D64-E9665788CAA3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{28DBA674-86D1-9884-5D64-E9665788CAA3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{28DBA674-86D1-9884-5D64-E9665788CAA3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{28DBA674-86D1-9884-5D64-E9665788CAA3}.Release|Any CPU.Build.0 = Release|Any CPU
		{727DFD98-0E53-094E-8D4B-6BCEF082A6E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{727DFD98-0E53-094E-8D4B-6BCEF082A6E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{727DFD98-0E53-094E-8D4B-6BCEF082A6E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{727DFD98-0E53-094E-8D4B-6BCEF082A6E3}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{50639FD1-2DA8-8D2E-C180-9133D440F1B5} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{0F51285B-777A-4ABD-F3B4-2C161F0C1045} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{20066E35-2BA1-1A18-00EC-7202CE90BFF4} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{4262B0E3-2620-1190-390E-3861ECE85572} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{28DBA674-86D1-9884-5D64-E9665788CAA3} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{727DFD98-0E53-094E-8D4B-6BCEF082A6E3} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {A3802377-621F-4878-8C9B-59C06EBC0585}
	EndGlobalSection
EndGlobal
