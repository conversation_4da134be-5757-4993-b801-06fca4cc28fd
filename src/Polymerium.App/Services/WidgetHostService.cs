using System.Collections;
using System.Collections.Generic;
using Polymerium.App.Widgets;

namespace Polymerium.App.Services;

// 这不是什么插件化的模块，所有小工具都是一次性集中封装的，因此后端共用同一个服务，后缀 Host 表示一对多
public class WidgetHostService(PersistenceService persistenceService)
{
    #region Private

    private IDictionary<string, WidgetContext> _cachedContexts = new Dictionary<string, WidgetContext>();

    #endregion

    public WidgetContext GetOrCreateContext(string widgetId)
    {
        if (_cachedContexts.TryGetValue(widgetId, out var context))
            return context;
        context = new WidgetContext(widgetId, this);
        _cachedContexts.Add(widgetId, context);
        return context;
    }

    #region Widget Delegates

    public bool IsWidgetPinned(string widgetId) => GetOrCreateContext(widgetId).IsPinned;

    #endregion
}