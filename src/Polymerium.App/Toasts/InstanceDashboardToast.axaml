<husk:Toast xmlns="https://github.com/avaloniaui"
            xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
            xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
            xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
            xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
            xmlns:local="using:Polymerium.App.Toasts"
            xmlns:launching="clr-namespace:Polymerium.Trident.Engines.Launching;assembly=Polymerium.Trident"
            xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
            xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
            xmlns:fie="clr-namespace:FluentIcons.Avalonia.MarkupExtensions;assembly=FluentIcons.Avalonia"
            xmlns:m="clr-namespace:Polymerium.App.Models"
            mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
            Header="Dashboard" Padding="0"
            x:Class="Polymerium.App.Toasts.InstanceDashboardToast"
            ScrollViewer.VerticalScrollBarVisibility="Disabled">
    <husk:Toast.Resources>
        <DataTemplate x:Key="ScrapTemplate" DataType="m:ScrapModel">
            <Border Background="Transparent">
                <Panel Margin="4,2">
                    <TextBlock Text="{Binding Message}" TextWrapping="Wrap" />
                    <StackPanel x:Name="InfoPanel" HorizontalAlignment="Right" VerticalAlignment="Top"
                                Orientation="Horizontal" Spacing="2">
                        <husk:Tag Content="{Binding Sender,FallbackValue=*}" FontSize="{StaticResource SmallFontSize}" />
                        <husk:Tag Content="{Binding Thread,FallbackValue=None}"
                                  FontSize="{StaticResource SmallFontSize}" />
                        <husk:Tag Content="{Binding Time, StringFormat=HH:mm:ss}"
                                  FontSize="{StaticResource SmallFontSize}" />
                        <StackPanel.Transitions>
                            <Transitions>
                                <DoubleTransition Property="Opacity" Easing="SineEaseInOut"
                                                  Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </StackPanel.Transitions>
                    </StackPanel>
                </Panel>
                <Border.Styles>
                    <Style Selector="Border:pointerover StackPanel#InfoPanel">
                        <Setter Property="Opacity" Value="0.0" />
                    </Style>
                </Border.Styles>
            </Border>
        </DataTemplate>
    </husk:Toast.Resources>
    <Grid RowDefinitions="*,0,Auto">
        <Border Grid.Row="0">
            <ScrollViewer x:Name="Viewer" Margin="0,72,0,0">
                <ItemsControl ItemsSource="{Binding $parent[local:InstanceDashboardToast].Bindable}">
                    <ItemsControl.ItemTemplate>
                        <DataTemplate DataType="m:ScrapModel">
                            <Panel>
                                <husk:SwitchPresenter Value="{Binding Level}" TargetType="launching:ScrapLevel">
                                    <husk:SwitchCase Value="Information">
                                        <Border BorderBrush="{StaticResource ControlBorderBrush}"
                                                BorderThickness="3,0,0,0"
                                                Background="{StaticResource ControlTranslucentFullBackgroundBrush}">
                                            <ContentControl Content="{Binding}"
                                                            ContentTemplate="{StaticResource ScrapTemplate}" />
                                        </Border>
                                    </husk:SwitchCase>
                                    <husk:SwitchCase Value="Warning">
                                        <Border BorderBrush="{StaticResource ControlWarningBorderBrush}"
                                                BorderThickness="3,0,0,0"
                                                Background="{StaticResource ControlWarningTranslucentHalfBackgroundBrush}">
                                            <ContentControl Content="{Binding}"
                                                            ContentTemplate="{StaticResource ScrapTemplate}"
                                                            Foreground="{StaticResource ControlWarningForegroundBrush}" />
                                        </Border>
                                    </husk:SwitchCase>
                                    <husk:SwitchCase Value="Error">
                                        <Border BorderBrush="{StaticResource ControlDangerBorderBrush}"
                                                BorderThickness="3,0,0,0"
                                                Background="{StaticResource ControlDangerTranslucentHalfBackgroundBrush}">
                                            <ContentControl Content="{Binding}"
                                                            ContentTemplate="{StaticResource ScrapTemplate}"
                                                            Foreground="{StaticResource ControlDangerForegroundBrush}" />
                                        </Border>
                                    </husk:SwitchCase>
                                </husk:SwitchPresenter>
                            </Panel>
                        </DataTemplate>
                    </ItemsControl.ItemTemplate>
                    <ItemsControl.ItemsPanel>
                        <ItemsPanelTemplate>
                            <VirtualizingStackPanel />
                        </ItemsPanelTemplate>
                    </ItemsControl.ItemsPanel>
                </ItemsControl>
            </ScrollViewer>
        </Border>
        <Grid Grid.Row="2" ColumnDefinitions="Auto,0,*,4,Auto" Margin="12">
            <TextBox Grid.Column="2" Watermark="Filter by content..."
                     Text="{Binding $parent[local:InstanceDashboardToast].FilterText,Mode=TwoWay}">
                <TextBox.InnerLeftContent>
                    <StackPanel Orientation="Horizontal">
                        <StackPanel Orientation="Horizontal">
                            <icons:PackIconLucide Margin="10,0"
                                                  Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                  Kind="Filter" Height="{StaticResource MediumFontSize}"
                                                  VerticalAlignment="Center" />
                            <husk:Divider Orientation="Vertical" />
                        </StackPanel>
                    </StackPanel>
                </TextBox.InnerLeftContent>
                <TextBox.InnerRightContent>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock VerticalAlignment="Center" Margin="0,0,8,0">
                            <Run
                                Text="{Binding $parent[local:InstanceDashboardToast].Bindable.Count,Mode=OneWay,FallbackValue=0}" />
                            <Run Text="/" />
                            <Run
                                Text="9527" />
                        </TextBlock>
                        <Button
                            IsVisible="{Binding $parent[TextBox].Text,Converter={x:Static StringConverters.IsNotNullOrEmpty}}"
                            Command="{Binding $parent[TextBox].Clear}"
                            Content="{fie:SymbolIcon Symbol=Dismiss,FontSize={StaticResource MediumFontSize}}"
                            Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </StackPanel>
                </TextBox.InnerRightContent>
            </TextBox>
            <ToggleButton Grid.Column="4" Classes="Small"
                          IsChecked="{Binding $parent[local:InstanceDashboardToast].IsAutoScroll,Mode=TwoWay}">
                <fi:SymbolIcon Symbol="TextboxAlignBottom" FontSize="{StaticResource LargeFontSize}" />
            </ToggleButton>
        </Grid>
    </Grid>
</husk:Toast>