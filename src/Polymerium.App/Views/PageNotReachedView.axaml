<husk:Page xmlns="https://github.com/avaloniaui"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
           xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
           xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
           xmlns:vm="using:Polymerium.App.ViewModels"
           mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450" Header="Page Cannot Be Reached"
           x:DataType="vm:PageNotReachedViewModel"
           x:Class="Polymerium.App.Views.PageNotReachedView">
    <StackPanel VerticalAlignment="Center">
        <StackPanel.Effect>
            <DropShadowEffect Opacity="0.1" BlurRadius="6" />
        </StackPanel.Effect>
        <Image Source="/Assets/Icons/Desert.png" Width="128" Height="128" HorizontalAlignment="Center" />
        <TextBlock Margin="48,12,48,12" Text="{Binding Message}"
                   TextAlignment="Center" FontSize="24"
                   FontWeight="{StaticResource ControlStrongFontWeight}"
                   TextWrapping="WrapWithOverflow" />
    </StackPanel>
</husk:Page>