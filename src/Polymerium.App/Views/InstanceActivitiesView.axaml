<controls:Subpage xmlns="https://github.com/avaloniaui"
                  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                  xmlns:controls="using:Polymerium.App.Controls"
                  xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                  xmlns:iconPacks="https://github.com/MahApps/IconPacks.Avalonia"
                  xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
                  xmlns:vm="clr-namespace:Polymerium.App.ViewModels"
                  xmlns:m="clr-namespace:Polymerium.App.Models"
                  xmlns:lvc="clr-namespace:LiveChartsCore.SkiaSharpView.Avalonia;assembly=LiveChartsCore.SkiaSharpView.Avalonia"
                  mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
                  x:Class="Polymerium.App.Views.InstanceActivitiesView"
                  ScrollViewer.VerticalScrollBarVisibility="Disabled" x:DataType="vm:InstanceActivitiesViewModel">
    <Grid RowDefinitions="256,*" RowSpacing="8">
        <Grid Grid.Row="0" ColumnDefinitions="212,*" ColumnSpacing="8">
            <Grid Grid.Column="0" RowDefinitions="48,*,Auto">
                <Border Grid.Row="1" Grid.RowSpan="2" Background="{StaticResource CardBackgroundBrush}"
                        BorderBrush="{StaticResource ControlBorderBrush}" BorderThickness="1"
                        CornerRadius="{StaticResource MediumCornerRadius}" />
                <Border Grid.Row="0" Grid.RowSpan="2" Margin="12,0"
                        Background="{StaticResource FlyoutBackgroundBrush}"
                        CornerRadius="{StaticResource MediumCornerRadius}"
                        BorderBrush="{StaticResource ControlAccentInteractiveBorderBrush}" BorderThickness="2,2,2,6">
                    <StackPanel VerticalAlignment="Center" Spacing="8">
                        <TextBlock Text="Active Days" HorizontalAlignment="Center"
                                   FontWeight="{StaticResource ControlStrongFontWeight}" />
                        <Border Background="{StaticResource ControlAccentInteractiveBackgroundBrush}"
                                HorizontalAlignment="Center" Padding="12,4"
                                CornerRadius="{StaticResource SmallCornerRadius}">
                            <TextBlock Text="#1"
                                       FontSize="{StaticResource ExtraLargeFontSize}"
                                       Foreground="{StaticResource FlyoutBackgroundBrush}" />
                        </Border>
                    </StackPanel>
                </Border>
                <Grid Grid.Row="2" ColumnDefinitions="*,Auto" Margin="12">
                    <TextBlock Text="Total Hours" />
                    <TextBlock Grid.Column="1" Text="{Binding TotalPlayTime,FallbackValue=128 hrs}"
                               FontSize="{StaticResource ExtraLargeFontSize}" />
                </Grid>
            </Grid>
            <husk:Card Grid.Column="1">
                <lvc:CartesianChart Series="{Binding WeekSeries}"
                                    XAxes="{Binding XAxes}"
                                    YAxes="{Binding YAxes}" />
            </husk:Card>
        </Grid>
        <Grid Grid.Row="1" ColumnDefinitions="*" ColumnSpacing="8">
            <husk:Card Grid.Column="0" Padding="0">
                <DockPanel>
                    <Grid DockPanel.Dock="Top" ColumnDefinitions="*,Auto"
                          Margin="{Binding Source={StaticResource CardContentMargin},Converter={x:Static husk:ThicknessConverters.WithoutBottom}}">
                        <TextBlock Grid.Column="0" Text="Changelog"
                                   FontWeight="{StaticResource ControlStrongFontWeight}"
                                   FontSize="{StaticResource LargeFontSize}" />
                        <TabStrip Grid.Column="1" Theme="{StaticResource SegmentedTabStripTheme}"
                                  SelectedIndex="{Binding SinceDayIndex,Mode=TwoWay}">
                            <TabStripItem Content="{x:Static lang:Resources.Enum_Day}" />
                            <TabStripItem Content="{x:Static lang:Resources.Enum_Week}" />
                            <TabStripItem Content="{x:Static lang:Resources.Enum_Month}" />
                            <TabStripItem Content="{x:Static lang:Resources.Enum_Year}" />
                            <TabStripItem Content="{x:Static lang:Resources.Enum_All}" />
                        </TabStrip>
                    </Grid>
                    <husk:LazyContainer Source="{Binding PagedActions}">
                        <husk:LazyContainer.SourceTemplate>
                            <DataTemplate DataType="m:InstanceActionCollection">
                                <Panel>
                                    <StackPanel VerticalAlignment="Center"
                                                Spacing="8"
                                                IsVisible="{Binding Count,Converter={x:Static husk:NumberConverters.IsZero},FallbackValue={x:True}}">
                                        <iconPacks:PackIconLucide Kind="Grid2x2"
                                                                  Height="{StaticResource ExtraLargeFontSize}"
                                                                  Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                                  Width="{StaticResource ExtraLargeFontSize}"
                                                                  HorizontalAlignment="Center" />
                                        <TextBlock Text="{x:Static lang:Resources.Shared_EmptyListLabelText}"
                                                   FontSize="{StaticResource LargeFontSize}"
                                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                   HorizontalAlignment="Center" />
                                    </StackPanel>
                                    <ScrollViewer>
                                        <ItemsControl Margin="{StaticResource CardContentMargin}"
                                                      ItemsSource="{Binding}">
                                            <ItemsControl.ItemsPanel>
                                                <ItemsPanelTemplate>
                                                    <StackPanel Spacing="6" />
                                                </ItemsPanelTemplate>
                                            </ItemsControl.ItemsPanel>
                                            <ItemsControl.ItemTemplate>
                                                <DataTemplate DataType="m:InstanceActionModel">
                                                    <controls:InstanceActionCard />
                                                </DataTemplate>
                                            </ItemsControl.ItemTemplate>
                                        </ItemsControl>
                                    </ScrollViewer>
                                </Panel>
                            </DataTemplate>
                        </husk:LazyContainer.SourceTemplate>
                    </husk:LazyContainer>
                </DockPanel>
            </husk:Card>
        </Grid>
    </Grid>
</controls:Subpage>