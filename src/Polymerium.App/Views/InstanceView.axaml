<controls:ScopedPage xmlns="https://github.com/avaloniaui"
                     xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                     xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                     xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                     xmlns:vm="using:Polymerium.App.ViewModels"
                     xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
                     xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                     xmlns:m="using:Polymerium.App.Models"
                     xmlns:controls="using:Polymerium.App.Controls"
                     xmlns:trident="clr-namespace:Polymerium.Trident;assembly=Polymerium.Trident"
                     mc:Ignorable="d" d:DesignWidth="700" d:DesignHeight="600" x:DataType="vm:InstanceViewModel"
                     Padding="0" ScrollViewer.VerticalScrollBarVisibility="Disabled" IsHeaderVisible="False"
                     Key="{Binding Basic.Key,Mode=OneWay}"
                     x:Class="Polymerium.App.Views.InstanceView">
    <Panel>
        <husk:SwitchPresenter Value="{Binding State,Mode=OneWay}" TargetType="trident:InstanceState">
            <husk:SwitchCase Value="Idle" />
            <husk:SwitchCase Value="Updating">
                <Rectangle Margin="-250" Opacity="0.2">
                    <Rectangle.Fill>
                        <VisualBrush DestinationRect="0,0,250,250" SourceRect="0,0,250,250" TileMode="Tile"
                                     Stretch="Fill">
                            <VisualBrush.Visual>
                                <Image Source="/Assets/Images/Patterns/Blue_Icons.png" Stretch="Fill" Height="250"
                                       Width="250" />
                            </VisualBrush.Visual>
                        </VisualBrush>
                    </Rectangle.Fill>
                    <Rectangle.Styles>
                        <Style Selector="Rectangle">
                            <Style.Animations>
                                <Animation Duration="0:0:5" IterationCount="Infinite">
                                    <KeyFrame Cue="0%">
                                        <Setter Property="TranslateTransform.Y" Value="250" />
                                    </KeyFrame>
                                    <KeyFrame Cue="100%">
                                        <Setter Property="TranslateTransform.Y" Value="0" />
                                    </KeyFrame>
                                </Animation>
                            </Style.Animations>
                        </Style>
                    </Rectangle.Styles>
                </Rectangle>
            </husk:SwitchCase>
            <husk:SwitchCase Value="Deploying">
                <Rectangle Margin="-250" Opacity="0.2">
                    <Rectangle.Fill>
                        <VisualBrush DestinationRect="0,0,250,250" SourceRect="0,0,250,250" TileMode="Tile"
                                     Stretch="Fill">
                            <VisualBrush.Visual>
                                <Image Source="/Assets/Images/Patterns/Yellow_Icons.png" Stretch="Fill" Height="250"
                                       Width="250" />
                            </VisualBrush.Visual>
                        </VisualBrush>
                    </Rectangle.Fill>
                    <Rectangle.Styles>
                        <Style Selector="Rectangle">
                            <Style.Animations>
                                <Animation Duration="0:0:5" IterationCount="Infinite">
                                    <KeyFrame Cue="0%">
                                        <Setter Property="TranslateTransform.X" Value="0" />
                                        <Setter Property="TranslateTransform.Y" Value="250" />
                                    </KeyFrame>
                                    <KeyFrame Cue="100%">
                                        <Setter Property="TranslateTransform.X" Value="250" />
                                        <Setter Property="TranslateTransform.Y" Value="0" />
                                    </KeyFrame>
                                </Animation>
                            </Style.Animations>
                        </Style>
                    </Rectangle.Styles>
                </Rectangle>
            </husk:SwitchCase>
            <husk:SwitchCase Value="Running">
                <Rectangle Margin="-250" Opacity="0.2">
                    <Rectangle.Fill>
                        <VisualBrush DestinationRect="0,0,250,250" SourceRect="0,0,250,250" TileMode="Tile"
                                     Stretch="Fill">
                            <VisualBrush.Visual>
                                <Image Source="/Assets/Images/Patterns/Green_Icons.png" Stretch="Fill" Height="250"
                                       Width="250" />
                            </VisualBrush.Visual>
                        </VisualBrush>
                    </Rectangle.Fill>
                    <Rectangle.Styles>
                        <Style Selector="Rectangle">
                            <Style.Animations>
                                <Animation Duration="0:0:5" IterationCount="Infinite">
                                    <KeyFrame Cue="0%">
                                        <Setter Property="TranslateTransform.X" Value="250" />
                                    </KeyFrame>
                                    <KeyFrame Cue="100%">
                                        <Setter Property="TranslateTransform.X" Value="0" />
                                    </KeyFrame>
                                </Animation>
                            </Style.Animations>
                        </Style>
                    </Rectangle.Styles>
                </Rectangle>
            </husk:SwitchCase>
        </husk:SwitchPresenter>
        <Grid ColumnDefinitions="Auto,0,*">
            <Border Grid.Column="0" Background="{StaticResource OverlayBackgroundBrush}"
                    CornerRadius="{StaticResource MediumCornerRadius}" Width="64" Margin="12,12,0,12">
                <Grid RowDefinitions="Auto,12,*,12,Auto,12,Auto,0,Auto">
                    <Button Grid.Row="0" Command="{Binding $parent[husk:Frame].GoBackCommand}" Margin="12">
                        <icons:PackIconLucide Kind="ArrowLeft" />
                    </Button>
                    <ListBox x:Name="EntryBox" Grid.Row="2" SelectionMode="Single" ItemsSource="{Binding PageEntries}"
                             SelectedValue="{Binding SelectedPage}"
                             Padding="0" SelectionChanged="EntryBox_OnSelectionChanged"
                             ClipToBounds="False">
                        <ListBox.ItemTemplate>
                            <DataTemplate DataType="m:InstanceSubpageEntryModel">
                                <icons:PackIconLucide Kind="{Binding Icon}" Height="18" HorizontalAlignment="Center" />
                            </DataTemplate>
                        </ListBox.ItemTemplate>
                        <ListBox.ItemContainerTheme>
                            <ControlTheme TargetType="ListBoxItem">
                                <Setter Property="Cursor" Value="Hand" />
                                <Setter Property="ClipToBounds" Value="False" />
                                <Setter Property="Template">
                                    <ControlTemplate>
                                        <Panel Background="{StaticResource TransparentBrush}">
                                            <Border Name="Indicator" Opacity="0"
                                                    Background="{StaticResource ControlAccentTranslucentHalfBackgroundBrush}">
                                                <Border.Transitions>
                                                    <Transitions>
                                                        <DoubleTransition Property="Opacity" Easing="CubicEaseOut"
                                                                          Duration="{StaticResource ControlFasterAnimationDuration}" />
                                                    </Transitions>
                                                </Border.Transitions>
                                            </Border>
                                            <Border Name="Lock" Opacity="0" BorderThickness="3,0"
                                                    BorderBrush="{StaticResource ControlAccentInteractiveBorderBrush}"
                                                    RenderTransform="scaleX(1.1)">
                                                <Border.Transitions>
                                                    <Transitions>
                                                        <DoubleTransition Property="Opacity"
                                                                          Duration="{StaticResource ControlFasterAnimationDuration}" />
                                                        <TransformOperationsTransition Property="RenderTransform"
                                                            Easing="CubicEaseOut"
                                                            Duration="{StaticResource ControlFasterAnimationDuration}" />
                                                    </Transitions>
                                                </Border.Transitions>
                                            </Border>
                                            <ContentPresenter Name="PART_ContentPresenter" Margin="8"
                                                              Content="{TemplateBinding Content}"
                                                              ContentTemplate="{TemplateBinding ContentTemplate}">
                                                <ContentPresenter.Transitions>
                                                    <Transitions>
                                                        <BrushTransition Property="Foreground"
                                                                         Duration="{StaticResource ControlFasterAnimationDuration}" />
                                                    </Transitions>
                                                </ContentPresenter.Transitions>
                                            </ContentPresenter>
                                        </Panel>
                                    </ControlTemplate>
                                </Setter>
                                <Style Selector="^:selected /template/ ContentPresenter#PART_ContentPresenter">
                                    <Setter Property="Foreground"
                                            Value="{StaticResource ControlAccentForegroundBrush}" />
                                </Style>
                                <Style Selector="^:pointerover /template/ Border#Indicator">
                                    <Setter Property="Opacity" Value="1" />
                                </Style>
                                <Style Selector="^:selected /template/ Border#Indicator">
                                    <Setter Property="Opacity" Value="1" />
                                </Style>
                                <Style Selector="^:selected /template/ Border#Lock">
                                    <Setter Property="Opacity" Value="1" />
                                    <Setter Property="RenderTransform" Value="scaleX(1.0)" />
                                </Style>
                            </ControlTheme>
                        </ListBox.ItemContainerTheme>
                        <ListBox.ItemsPanel>
                            <ItemsPanelTemplate>
                                <StackPanel Spacing="4" />
                            </ItemsPanelTemplate>
                        </ListBox.ItemsPanel>
                    </ListBox>
                    <Button Grid.Row="6" Command="{Binding OpenFolderCommand}" Margin="12,0">
                        <icons:PackIconLucide Kind="Folder" />
                    </Button>
                    <Border Grid.Row="8" CornerRadius="{StaticResource SmallCornerRadius}" Margin="8"
                            BorderBrush="{StaticResource ControlBorderBrush}" BorderThickness="2"
                            Height="{Binding $self.Bounds.Width}"
                            BackgroundSizing="InnerBorderEdge">
                        <Border.Background>
                            <ImageBrush Source="{Binding Basic.Thumbnail}" />
                        </Border.Background>
                    </Border>
                </Grid>
            </Border>
            <Panel Grid.Column="2">
                <husk:Frame x:Name="Frame" />
            </Panel>
        </Grid>
    </Panel>
</controls:ScopedPage>