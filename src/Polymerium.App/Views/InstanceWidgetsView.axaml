<controls:Subpage xmlns="https://github.com/avaloniaui"
                  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                  xmlns:controls="using:Polymerium.App.Controls"
                  xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                  xmlns:vm="clr-namespace:Polymerium.App.ViewModels"
                  xmlns:widgets="clr-namespace:Polymerium.App.Widgets"
                  xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
                  xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
                  mc:Ignorable="d" d:DesignWidth="600" d:DesignHeight="400"
                  x:Class="Polymerium.App.Views.InstanceWidgetsView" x:DataType="vm:InstanceWidgetsViewModel">
    <Grid>
        <ItemsControl ItemsSource="{Binding Widgets}" VerticalAlignment="Top">
            <ItemsControl.ItemTemplate>
                <DataTemplate x:DataType="widgets:WidgetBase">
                    <Grid RowDefinitions="Auto,*">
                        <Border Grid.Row="0"
                                Background="{StaticResource ControlAccentTranslucentHalfBackgroundBrush}"
                                BorderThickness="1" BorderBrush="{StaticResource ControlAccentBorderBrush}"
                                Padding="12,4"
                                CornerRadius="{Binding Source={StaticResource MediumCornerRadius},Converter={x:Static husk:CornerRadiusConverters.Upper}}">
                            <Grid ColumnDefinitions="Auto,*,Auto" ColumnSpacing="12">
                                <!-- Grab Handle: Avalonia 没有 Grab 指针，而且本来就没法 ReOrder 所以这个图标仅供装饰 -->
                                <Border Grid.Column="0" Cursor="SizeAll">
                                    <fi:SymbolIcon Symbol="ReOrderDotsVertical"
                                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                   FontSize="{StaticResource MediumFontSize}" />
                                </Border>
                                <TextBlock Grid.Column="1" Text="{Binding Title}"
                                           VerticalAlignment="Center" />
                                <ToggleButton Grid.Column="2" Theme="{StaticResource GhostToggleButtonTheme}"
                                              IsEnabled="{Binding SlimTemplate,Converter={x:Static ObjectConverters.IsNotNull}}"
                                              IsChecked="{Binding IsPinned}" Classes="Small" Margin="4">
                                    <icons:PackIconLucide Kind="Pin" Width="{StaticResource SmallFontSize}"
                                                          Height="{StaticResource SmallFontSize}" />
                                </ToggleButton>
                            </Grid>
                        </Border>
                        <Border Grid.Row="1" BorderThickness="1,0,1,1"
                                Background="{StaticResource CardBackgroundBrush}"
                                BorderBrush="{StaticResource ControlBorderBrush}"
                                Padding="12"
                                CornerRadius="{Binding Source={StaticResource MediumCornerRadius},Converter={x:Static husk:CornerRadiusConverters.Lower}}">
                            <ContentControl Content="{Binding}" ContentTemplate="{Binding FullTemplate}" />
                        </Border>
                    </Grid>
                </DataTemplate>
            </ItemsControl.ItemTemplate>
            <ItemsControl.ItemContainerTheme>
                <ControlTheme TargetType="ContentPresenter">
                    <Setter Property="MaxWidth" Value="512" />
                    <Setter Property="MinWidth" Value="256" />
                    <Setter Property="MaxHeight" Value="512" />
                    <Setter Property="MinHeight" Value="256" />
                </ControlTheme>
            </ItemsControl.ItemContainerTheme>
            <ItemsControl.ItemsPanel>
                <ItemsPanelTemplate>
                    <husk:FlexWrapPanel ColumnSpacing="12" RowSpacing="12" />
                </ItemsPanelTemplate>
            </ItemsControl.ItemsPanel>
        </ItemsControl>
    </Grid>
</controls:Subpage>