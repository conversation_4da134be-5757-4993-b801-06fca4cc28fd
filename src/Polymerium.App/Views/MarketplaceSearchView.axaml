<husk:Page xmlns="https://github.com/avaloniaui"
           xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
           xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
           xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
           xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
           xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
           xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
           xmlns:vm="using:Polymerium.App.ViewModels"
           xmlns:v="using:Polymerium.App.Views"
           xmlns:m="using:Polymerium.App.Models"
           xmlns:controls="clr-namespace:Polymerium.App.Controls"
           xmlns:collection="clr-namespace:System.Collections.Generic;assembly=System.Collections"
           xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
           xmlns:fie="clr-namespace:FluentIcons.Avalonia.MarkupExtensions;assembly=FluentIcons.Avalonia"
           ScrollViewer.VerticalScrollBarVisibility="Disabled"
           mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450" IsHeaderVisible="False"
           x:DataType="vm:MarketplaceSearchViewModel" Padding="0"
           x:Class="Polymerium.App.Views.MarketplaceSearchView" x:Name="Exhibition">
    <husk:Page.Resources>
        <MenuFlyout x:Key="ExhibitModpackButtonFlyout" x:DataType="m:ExhibitModel">
            <MenuItem Header="{x:Static lang:Resources.MarketplaceSearchView_OpenWebsiteMenuText}"
                      Command="{Binding $parent[v:MarketplaceSearchView].((vm:MarketplaceSearchViewModel)DataContext).OpenWebsiteCommand,FallbackValue={x:Null}}"
                      CommandParameter="{Binding}">
                <MenuItem.Icon>
                    <icons:PackIconLucide Kind="ExternalLink"
                                          Height="{StaticResource MediumFontSize}"
                                          Width="{StaticResource MediumFontSize}"
                                          VerticalAlignment="Center" />
                </MenuItem.Icon>
            </MenuItem>
            <!-- <MenuItem Header="Save to Favorites"> -->
            <!--     <MenuItem.Icon> -->
            <!--         <icons:PackIconLucide Kind="Bookmark" -->
            <!--             Height="10" Width="10" -->
            <!--             VerticalAlignment="Center" /> -->
            <!--     </MenuItem.Icon> -->
            <!-- </MenuItem> -->
        </MenuFlyout>
    </husk:Page.Resources>
    <Panel>
        <Grid RowDefinitions="Auto,0,*">
            <StackPanel Grid.Row="0" Margin="{StaticResource PageHeaderlessContentMargin}" Spacing="6">
                <Border CornerRadius="{OnPlatform Default=3,Windows=5}" Padding="{StaticResource CardContentMargin}"
                        Margin="-10,-10,-10,0">
                    <Border.Background>
                        <ImageBrush Source="{Binding HeaderImage}" Stretch="Fill" />
                    </Border.Background>
                    <StackPanel Spacing="12">
                        <StackPanel Orientation="Horizontal" Spacing="12">
                            <Button Theme="{StaticResource GhostButtonTheme}"
                                    Command="{Binding $parent[husk:Frame].GoBackCommand}">
                                <fi:SymbolIcon Symbol="ArrowLeft" FontSize="{StaticResource MediumFontSize}" />
                            </Button>
                            <TextBlock Text="{x:Static lang:Resources.MarketplaceSearchView_Title}"
                                       FontSize="{StaticResource LargeFontSize}"
                                       VerticalAlignment="Center" />
                        </StackPanel>
                        <Grid ColumnDefinitions="*,4,Auto,4,Auto" VerticalAlignment="Center" MaxWidth="720"
                              Margin="64,0,64,0">
                            <TextBox Grid.Column="0"
                                     Watermark="{x:Static lang:Resources.MarketplaceSearchView_SearchBarPlaceholder}"
                                     BorderBrush="{StaticResource OverlayInteractiveBackgroundBrush}"
                                     Text="{Binding QueryText,Mode=TwoWay}">
                                <TextBox.InnerRightContent>
                                    <Button
                                        IsVisible="{Binding $parent[TextBox].Text,Converter={x:Static StringConverters.IsNotNullOrEmpty}}"
                                        Command="{Binding $parent[TextBox].Clear}"
                                        Content="{fie:SymbolIcon Symbol=Dismiss,FontSize={StaticResource MediumFontSize}}"
                                        Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                </TextBox.InnerRightContent>
                            </TextBox>
                            <ComboBox Grid.Column="2" ItemsSource="{Binding Repositories}"
                                      BorderBrush="{StaticResource OverlayInteractiveBackgroundBrush}"
                                      SelectedItem="{Binding SelectedRepository,Mode=TwoWay}">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate DataType="m:RepositoryBasicModel">
                                        <TextBlock Text="{Binding Name}" />
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                            <Button Grid.Column="4" Classes="Primary" IsDefault="True"
                                    Command="{Binding SearchCommand}"
                                    IsEnabled="{Binding Exhibits.IsFetching,Converter={x:Static BoolConverters.Not},FallbackValue=False}">
                                <StackPanel Orientation="Horizontal" Spacing="6">
                                    <husk:SwitchPresenter VerticalAlignment="Center"
                                                          Value="{Binding Exhibits.IsFetching,Mode=OneWay,FallbackValue=True}"
                                                          TargetType="x:Boolean"
                                                          Height="{StaticResource MediumFontSize}"
                                                          Width="{StaticResource MediumFontSize}">
                                        <husk:SwitchCase Value="True">
                                            <husk:ProgressRing
                                                Theme="{StaticResource SolidProgressRingTheme}"
                                                IsIndeterminate="{Binding Exhibits.IsFetching,Mode=OneWay,FallbackValue=False}"
                                                StrokeWidth="2"
                                                TrackStrokeWidth="2"
                                                Background="{Binding  $parent[Button].Foreground}" />
                                        </husk:SwitchCase>
                                        <husk:SwitchCase Value="False">
                                            <icons:PackIconLucide Kind="Search" />
                                        </husk:SwitchCase>
                                    </husk:SwitchPresenter>
                                    <TextBlock Text="{x:Static lang:Resources.MarketplaceSearchView_SearchButtonText}"
                                               VerticalAlignment="Center" />
                                </StackPanel>
                            </Button>
                        </Grid>
                    </StackPanel>
                </Border>
                <Grid ColumnDefinitions="*,Auto" Name="FilterBar">
                    <StackPanel Grid.Column="0" Orientation="Horizontal" Spacing="4">
                        <ComboBox ItemsSource="{Binding SelectedRepository.Versions}"
                                  SelectedItem="{Binding FilteredVersion,Mode=TwoWay}" PlaceholderText="Version">
                            <ComboBox.ItemTemplate>
                                <DataTemplate DataType="x:String">
                                    <TextBlock Text="{Binding}" />
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>
                        <ComboBox ItemsSource="{Binding SelectedRepository.Loaders}"
                                  SelectedItem="{Binding FilteredLoader,Mode=TwoWay}" PlaceholderText="Loader">
                            <ComboBox.ItemTemplate>
                                <DataTemplate DataType="m:LoaderBasicModel">
                                    <TextBlock Text="{Binding DisplayName}" />
                                </DataTemplate>
                            </ComboBox.ItemTemplate>
                        </ComboBox>
                        <TextBlock VerticalAlignment="Center" Margin="8,0">
                            <Run Text="{x:Static lang:Resources.MarketplaceSearchView_ResultCountLabelText}"
                                 Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                            <Run Text="{Binding Exhibits.Count,FallbackValue=0}" />
                        </TextBlock>
                    </StackPanel>
                    <StackPanel Grid.Column="1" Orientation="Horizontal" Spacing="12">
                        <HyperlinkButton VerticalAlignment="Center" Command="{Binding ClearFiltersCommand}">
                            <StackPanel Orientation="Horizontal" Spacing="4">
                                <fi:SymbolIcon Symbol="FilterDismiss" FontSize="{StaticResource MediumFontSize}" />
                                <TextBlock Text="{x:Static lang:Resources.MarketplaceSearchView_ResetFilterButtonText}" />
                            </StackPanel>
                        </HyperlinkButton>
                        <TabStrip Name="LayoutSelector" SelectedIndex="{Binding LayoutIndex,Mode=TwoWay}"
                                  Theme="{StaticResource SegmentedTabStripTheme}">
                            <TabStrip.ItemsSource>
                                <collection:List x:TypeArguments="m:ListTemplateCombinationModel">
                                    <m:ListTemplateCombinationModel
                                        Icon="LayoutList">
                                        <m:ListTemplateCombinationModel.ItemTemplate>
                                            <DataTemplate x:DataType="m:ExhibitModel">
                                                <controls:ExhibitModpackButton
                                                    ContextFlyout="{StaticResource ExhibitModpackButtonFlyout}"
                                                    Theme="{StaticResource ListExhibitModpackButtonTheme}"
                                                    Command="{Binding $parent[v:MarketplaceSearchView].((vm:MarketplaceSearchViewModel)DataContext).ViewModpackCommand,FallbackValue={x:Null}}"
                                                    CommandParameter="{Binding}" />
                                            </DataTemplate>
                                        </m:ListTemplateCombinationModel.ItemTemplate>
                                        <m:ListTemplateCombinationModel.ItemsPanelTemplate>
                                            <ItemsPanelTemplate>
                                                <StackPanel Margin="16,0" Spacing="8" />
                                            </ItemsPanelTemplate>
                                        </m:ListTemplateCombinationModel.ItemsPanelTemplate>
                                    </m:ListTemplateCombinationModel>
                                    <m:ListTemplateCombinationModel
                                        Icon="LayoutGrid">
                                        <m:ListTemplateCombinationModel.ItemTemplate>
                                            <DataTemplate x:DataType="m:ExhibitModel">
                                                <controls:ExhibitModpackButton
                                                    ContextFlyout="{StaticResource ExhibitModpackButtonFlyout}"
                                                    Theme="{StaticResource GridExhibitModpackButtonTheme}"
                                                    Command="{Binding $parent[v:MarketplaceSearchView].((vm:MarketplaceSearchViewModel)DataContext).ViewModpackCommand,FallbackValue={x:Null}}"
                                                    CommandParameter="{Binding}" />
                                            </DataTemplate>
                                        </m:ListTemplateCombinationModel.ItemTemplate>
                                        <m:ListTemplateCombinationModel.ItemsPanelTemplate>
                                            <ItemsPanelTemplate>
                                                <husk:FlexWrapPanel ColumnSpacing="8" RowSpacing="8" Margin="16,0" />
                                            </ItemsPanelTemplate>
                                        </m:ListTemplateCombinationModel.ItemsPanelTemplate>
                                        <m:ListTemplateCombinationModel.ItemContainerTheme>
                                            <ControlTheme TargetType="ContentPresenter">
                                                <Setter Property="MaxWidth" Value="396" />
                                                <Setter Property="MinWidth" Value="198" />
                                                <Setter Property="MaxHeight" Value="256" />
                                                <Setter Property="MinHeight" Value="128" />
                                            </ControlTheme>
                                        </m:ListTemplateCombinationModel.ItemContainerTheme>
                                    </m:ListTemplateCombinationModel>
                                </collection:List>
                            </TabStrip.ItemsSource>
                            <TabStrip.ItemTemplate>
                                <DataTemplate x:DataType="m:ListTemplateCombinationModel">
                                    <icons:PackIconLucide Kind="{Binding Icon}"
                                                          Height="{StaticResource MediumFontSize}"
                                                          Width="{StaticResource MediumFontSize}"
                                                          VerticalAlignment="Center" HorizontalAlignment="Center" />
                                </DataTemplate>
                            </TabStrip.ItemTemplate>
                        </TabStrip>
                    </StackPanel>
                </Grid>
            </StackPanel>
            <Panel Grid.Row="2">
                <StackPanel VerticalAlignment="Center"
                            Spacing="8">
                    <StackPanel.IsVisible>
                        <MultiBinding Converter="{x:Static BoolConverters.And}">
                            <Binding Path="Exhibits.IsFetching" Mode="OneWay" Converter="{x:Static BoolConverters.Not}"
                                     FallbackValue="{x:True}" />
                            <Binding Path="Exhibits.Count" Mode="OneWay"
                                     Converter="{x:Static husk:NumberConverters.IsZero}"
                                     FallbackValue="{x:True}" />
                        </MultiBinding>
                    </StackPanel.IsVisible>
                    <icons:PackIconLucide Kind="Package2" Height="{StaticResource ExtraLargeFontSize}"
                                          Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                          Width="{StaticResource ExtraLargeFontSize}"
                                          HorizontalAlignment="Center" />
                    <TextBlock Text="{x:Static lang:Resources.Shared_EmptyListLabelText}"
                               FontSize="{StaticResource LargeFontSize}"
                               Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                               HorizontalAlignment="Center" />
                </StackPanel>
                <husk:InfiniteScrollView Margin="0,-12,0,0" ItemsSource="{Binding Exhibits}"
                                         ItemTemplate="{Binding #LayoutSelector.((m:ListTemplateCombinationModel)SelectedItem).ItemTemplate,FallbackValue={x:Null}}"
                                         ItemsPanel="{Binding #LayoutSelector.((m:ListTemplateCombinationModel)SelectedItem).ItemsPanelTemplate,FallbackValue={x:Null}}"
                                         ItemContainerTheme="{Binding #LayoutSelector.((m:ListTemplateCombinationModel)SelectedItem).ItemContainerTheme,FallbackValue={x:Null}}">
                    <husk:InfiniteScrollView.PendingContent>
                        <StackPanel Margin="36" Spacing="6">
                            <!-- BUG: https://github.com/AvaloniaUI/Avalonia/issues/18358 -->
                            <!-- 由于这个问题，当全屏上一层 Blur 时会触发全屏刷新 -->
                            <husk:ProgressRing Width="20" Height="20"
                                               IsIndeterminate="{Binding Exhibits.IsFetching,Mode=OneWay,FallbackValue=False}"
                                               HorizontalAlignment="Center" ShowProgressText="False" />
                            <TextBlock Text="{x:Static lang:Resources.Shared_FetchingLabelText}"
                                       HorizontalAlignment="Center" />
                        </StackPanel>
                    </husk:InfiniteScrollView.PendingContent>
                </husk:InfiniteScrollView>
            </Panel>
        </Grid>
    </Panel>

    <husk:Page.Styles>
        <Style Selector=":loading Grid#FilterBar">
            <Setter Property="IsVisible" Value="False" />
        </Style>
    </husk:Page.Styles>
</husk:Page>