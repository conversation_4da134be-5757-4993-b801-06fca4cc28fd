<controls:Subpage xmlns="https://github.com/avaloniaui"
                  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                  xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                  xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                  xmlns:controls="using:Polymerium.App.Controls"
                  xmlns:vm="using:Polymerium.App.ViewModels"
                  xmlns:components="using:Polymerium.App.Components"
                  xmlns:fi="using:FluentIcons.Avalonia"
                  xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
                  xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
                  mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
                  x:Class="Polymerium.App.Views.InstancePropertiesView" x:DataType="vm:InstancePropertiesViewModel">
    <StackPanel MaxWidth="1440" Spacing="12" Margin="24">
        <controls:SettingsEntry Icon="AppTitle" Title="{x:Static lang:Resources.InstancePropertiesView_TitleTitle}"
                                Summary="{x:Static lang:Resources.InstancePropertiesView_TitleSubtitle}">
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.InstancePropertiesView_DisplayNameLabelText}">
                <Border Background="{StaticResource ControlBackgroundBrush}"
                        CornerRadius="{StaticResource MediumCornerRadius}">
                    <StackPanel Margin="3" Spacing="3">
                        <Border Background="{StaticResource OverlaySolidBackgroundBrush}"
                                CornerRadius="{StaticResource SmallCornerRadius}">
                            <TextBlock Text="{Binding NameOverwrite,FallbackValue=Name}" Margin="8"
                                       FontSize="{StaticResource LargeFontSize}" TextAlignment="Center"
                                       TextWrapping="Wrap" />
                        </Border>
                        <Button Classes="Small" Theme="{StaticResource OutlineButtonTheme}"
                                Command="{Binding RenameInstanceCommand}"
                                Content="{x:Static lang:Resources.InstancePropertiesView_RenameButtonText}" />
                    </StackPanel>
                </Border>
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.InstancePropertiesView_ThumbnailLabelText}">
                <Grid ColumnDefinitions="Auto,12,*">
                    <Border Grid.Column="0" Padding="8"
                            Width="72"
                            Height="72"
                            HorizontalAlignment="Center"
                            Background="{StaticResource LayerBackgroundBrush}"
                            BorderBrush="{x:Null}"
                            CornerRadius="{StaticResource SmallCornerRadius}">
                        <Border.Effect>
                            <DropShadowEffect Opacity="0.1" />
                        </Border.Effect>
                        <Border Name="ThumbnailBox" CornerRadius="{StaticResource SmallCornerRadius}">
                            <Border.Background>
                                <ImageBrush Source="{Binding ThumbnailOverwrite}" />
                            </Border.Background>
                        </Border>

                    </Border>
                    <StackPanel Grid.Column="2" Spacing="12" VerticalAlignment="Center">
                        <Button Classes="Small" Command="{Binding SelectThumbnailCommand}">
                            <StackPanel Orientation="Horizontal" Spacing="4">
                                <icons:PackIconLucide Kind="File"
                                                      Height="{StaticResource SmallFontSize}"
                                                      Width="{StaticResource SmallFontSize}"
                                                      VerticalAlignment="Center" />
                                <TextBlock Text="{x:Static lang:Resources.InstancePropertiesView_SelectButtonText}" />
                            </StackPanel>
                        </Button>
                        <Button Classes="Small" Command="{Binding RemoveThumbnailCommand}">
                            <StackPanel Orientation="Horizontal" Spacing="4">
                                <icons:PackIconLucide Kind="Trash2"
                                                      Height="{StaticResource SmallFontSize}"
                                                      Width="{StaticResource SmallFontSize}"
                                                      VerticalAlignment="Center" />
                                <TextBlock Text="{x:Static lang:Resources.InstancePropertiesView_RemoveButtonText}" />
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </Grid>
            </controls:SettingsEntryItem>
        </controls:SettingsEntry>
        <husk:Divider />
        <controls:SettingsEntry Title="{x:Static lang:Resources.InstancePropertiesView_OverridesTitle}"
                                Summary="{x:Static lang:Resources.InstancePropertiesView_OverridesSubtitle}"
                                Icon="Games">
            <Button Command="{Binding GotoSettingsCommand}">
                <Grid ColumnDefinitions="Auto,8,*">
                    <fi:SymbolIcon Grid.Column="0" Symbol="DocumentOnePage" />
                    <StackPanel Grid.Column="2" VerticalAlignment="Center">
                        <TextBlock Text="{x:Static lang:Resources.InstancePropertiesView_NavigateToGlobalButtonTitle}" />
                        <TextBlock
                            Text="{x:Static lang:Resources.InstancePropertiesView_NavigateToGlobalButtonSubtitle}"
                            FontSize="{StaticResource SmallFontSize}"
                            Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </StackPanel>
                </Grid>
            </Button>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.InstancePropertiesView_JavaLabelText}">
                <TextBox Watermark="{Binding JavaHomeWatermark}"
                         Text="{Binding JavaHomeOverride,Mode=TwoWay,FallbackValue={x:Null}}">
                    <TextBox.InnerRightContent>
                        <StackPanel Orientation="Horizontal">
                            <Button Command="{Binding $parent[TextBox].Clear}">
                                <icons:PackIconLucide Kind="Delete" Height="{StaticResource SmallFontSize}" />
                            </Button>
                            <Button Command="{Binding PickFileCommand}"
                                    CommandParameter="{Binding $parent[TextBox]}">
                                <icons:PackIconLucide Kind="FileOutput" Height="{StaticResource MediumFontSize}" />
                            </Button>
                        </StackPanel>
                    </TextBox.InnerRightContent>
                </TextBox>
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem
                Header="{x:Static lang:Resources.InstancePropertiesView_JavaMaxMemoryLabelText}">
                <TextBox
                    Text="{Binding JavaMaxMemoryOverride,Mode=TwoWay}"
                    Watermark="{Binding JavaMaxMemoryWatermark}">
                    <TextBox.InnerRightContent>
                        <StackPanel Orientation="Horizontal">
                            <husk:Divider Orientation="Vertical" />
                            <TextBlock Text="{x:Static lang:Resources.InstancePropertiesView_JavaMaxMemoryUnitText}"
                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                       Margin="8,0" VerticalAlignment="Center" />
                        </StackPanel>
                    </TextBox.InnerRightContent>
                </TextBox>
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem
                Header="{x:Static lang:Resources.InstancePropertiesView_JavaAdditionalArgumentsLabelText}">
                <TextBox Text="{Binding JavaAdditionalArgumentsOverride,Mode=TwoWay}"
                         Watermark="{Binding JavaAdditionalArgumentsWatermark}" />
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem
                Header="{x:Static lang:Resources.InstancePropertiesView_WindowInitialSizeLabelText}">
                <Grid ColumnDefinitions="*,7,*">
                    <TextBox Grid.Column="0" Text="{Binding WindowInitialWidthOverride,Mode=TwoWay}"
                             Watermark="{Binding WindowInitialWidthWatermark}">
                        <TextBox.InnerLeftContent>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="{x:Static lang:Resources.InstancePropertiesView_WindowWidthLabelText}"
                                           Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                           Margin="8,0" VerticalAlignment="Center" />
                                <husk:Divider Orientation="Vertical" />
                            </StackPanel>
                        </TextBox.InnerLeftContent>
                    </TextBox>
                    <TextBox Grid.Column="2" Text="{Binding WindowInitialHeightOverride,Mode=TwoWay}"
                             Watermark="{Binding WindowInitialHeightWatermark}">
                        <TextBox.InnerLeftContent>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock
                                    Text="{x:Static lang:Resources.InstancePropertiesView_WindowHeightLabelText}"
                                    Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                    Margin="8,0" VerticalAlignment="Center" />
                                <husk:Divider Orientation="Vertical" />
                            </StackPanel>
                        </TextBox.InnerLeftContent>
                    </TextBox>
                </Grid>
            </controls:SettingsEntryItem>
        </controls:SettingsEntry>
        <husk:Divider />
        <controls:SettingsEntry Icon="HardDrive" Title="Persistence Storage (WIP)"
                                Summary="Files or folders that are shared across builds">
            <controls:SettingsEntryItem Header="Folders">
                <StackPanel Spacing="4">
                    <Border Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                            CornerRadius="{StaticResource MediumCornerRadius}" Padding="3">
                        <ItemsControl>
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel Spacing="3" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <Border CornerRadius="{StaticResource SmallCornerRadius}"
                                    Background="{StaticResource OverlaySolidBackgroundBrush}">
                                <DockPanel>
                                    <Button DockPanel.Dock="Right" Theme="{StaticResource GhostButtonTheme}"
                                            Classes="Small"
                                            Margin="4" Padding="6">
                                        <fi:SymbolIcon Symbol="Dismiss" HorizontalAlignment="Center"
                                                       FontSize="{StaticResource MediumFontSize}" />
                                    </Button>
                                    <Grid ColumnDefinitions="Auto,*" ColumnSpacing="12" Margin="12,0"
                                          VerticalAlignment="Center">
                                        <fi:SymbolIcon Grid.Column="0" Symbol="Folder"
                                                       FontSize="{StaticResource MediumFontSize}"
                                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                        <TextBlock Grid.Column="1" Text="saves" />
                                    </Grid>
                                </DockPanel>
                            </Border>
                            <Border CornerRadius="{StaticResource SmallCornerRadius}"
                                    Background="{StaticResource OverlaySolidBackgroundBrush}">
                                <DockPanel>
                                    <Button DockPanel.Dock="Right" Theme="{StaticResource GhostButtonTheme}"
                                            Classes="Small"
                                            Margin="4" Padding="6">
                                        <fi:SymbolIcon Symbol="Dismiss" HorizontalAlignment="Center"
                                                       FontSize="{StaticResource MediumFontSize}" />
                                    </Button>
                                    <Grid ColumnDefinitions="Auto,*" ColumnSpacing="12" Margin="12,0"
                                          VerticalAlignment="Center">
                                        <fi:SymbolIcon Grid.Column="0" Symbol="Folder"
                                                       FontSize="{StaticResource MediumFontSize}"
                                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                        <TextBlock Grid.Column="1" Text="defaultconfigs" />
                                    </Grid>
                                </DockPanel>
                            </Border>
                            <Border CornerRadius="{StaticResource SmallCornerRadius}"
                                    Background="{StaticResource OverlaySolidBackgroundBrush}">
                                <DockPanel>
                                    <Button DockPanel.Dock="Right" Theme="{StaticResource GhostButtonTheme}"
                                            Classes="Small"
                                            Margin="4" Padding="6">
                                        <fi:SymbolIcon Symbol="Dismiss" HorizontalAlignment="Center"
                                                       FontSize="{StaticResource MediumFontSize}" />
                                    </Button>
                                    <Grid ColumnDefinitions="Auto,*" ColumnSpacing="12" Margin="12,0"
                                          VerticalAlignment="Center">
                                        <fi:SymbolIcon Grid.Column="0" Symbol="Folder"
                                                       FontSize="{StaticResource MediumFontSize}"
                                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                        <TextBlock Grid.Column="1">
                                            <Run Text="config/"
                                                 Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                            <Run Text="jamd" />
                                        </TextBlock>
                                    </Grid>
                                </DockPanel>
                            </Border>
                        </ItemsControl>
                    </Border>
                    <Button Classes="Small" Content="Add Folder" />
                </StackPanel>
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="Files">
                <StackPanel Spacing="4">
                    <Border Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                            CornerRadius="{StaticResource MediumCornerRadius}" Padding="3">
                        <ItemsControl>
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel Spacing="3" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                            <Border CornerRadius="{StaticResource SmallCornerRadius}"
                                    Background="{StaticResource OverlaySolidBackgroundBrush}">
                                <DockPanel>
                                    <Button DockPanel.Dock="Right" Theme="{StaticResource GhostButtonTheme}"
                                            Classes="Small"
                                            Margin="4" Padding="6">
                                        <fi:SymbolIcon Symbol="Dismiss" HorizontalAlignment="Center"
                                                       FontSize="{StaticResource MediumFontSize}" />
                                    </Button>
                                    <Grid ColumnDefinitions="Auto,*" ColumnSpacing="12" Margin="12,0"
                                          VerticalAlignment="Center">
                                        <fi:SymbolIcon Grid.Column="0" Symbol="Document"
                                                       FontSize="{StaticResource MediumFontSize}"
                                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                        <TextBlock Grid.Column="1" Text="options.txt" />
                                    </Grid>
                                </DockPanel>
                            </Border>

                            <Border CornerRadius="{StaticResource SmallCornerRadius}"
                                    Background="{StaticResource OverlaySolidBackgroundBrush}">
                                <DockPanel>
                                    <Button DockPanel.Dock="Right" Theme="{StaticResource GhostButtonTheme}"
                                            Classes="Small"
                                            Margin="4" Padding="6">
                                        <fi:SymbolIcon Symbol="Dismiss" HorizontalAlignment="Center"
                                                       FontSize="{StaticResource MediumFontSize}" />
                                    </Button>
                                    <Grid ColumnDefinitions="Auto,*" ColumnSpacing="12" Margin="12,0"
                                          VerticalAlignment="Center">
                                        <fi:SymbolIcon Grid.Column="0" Symbol="Document"
                                                       FontSize="{StaticResource MediumFontSize}"
                                                       Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                        <TextBlock Grid.Column="1">
                                            <Run Text="config/"
                                                 Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                            <Run Text="miner.json" />
                                        </TextBlock>
                                    </Grid>
                                </DockPanel>
                            </Border>
                        </ItemsControl>
                    </Border>
                    <Button Classes="Small" Content="Add File" />
                </StackPanel>
            </controls:SettingsEntryItem>
        </controls:SettingsEntry>
        <husk:Divider />
        <controls:SettingsEntry Icon="Camera" Title="Snapshots (WIP)" Summary="Backup, yes, it's called backup">
            <controls:SettingsEntryItem Header="Taken Automatically When">
                <ComboBox SelectedIndex="0">
                    <ComboBoxItem Content="Never" />
                    <ComboBoxItem Content="Built" />
                </ComboBox>
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="Folders">
                <TextBox Watermark="Replace this with a TokenTextBox!!!" />
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="Files">
                <TextBox Watermark="Replace this with a TokenTextBox!!!" />
            </controls:SettingsEntryItem>
        </controls:SettingsEntry>
        <husk:Divider />
        <controls:SettingsEntry Icon="Handshake"
                                Title="{x:Static lang:Resources.InstancePropertiesView_BehaviorsTitle}"
                                Summary="{x:Static lang:Resources.InstancePropertiesView_BehaviorsSubtitle}">
            <controls:SettingsEntryItem
                Header="{x:Static lang:Resources.InstancePropertiesView_ResolvePackageDependenciesLabelText}">
                <StackPanel Spacing="8">
                    <husk:InfoBar
                        Content="{x:Static lang:Resources.InstancePropertiesView_ResolvePackageDependenciesPrompt}"
                        Classes="Warning" />
                    <ToggleSwitch
                        OnContent="{x:Static lang:Resources.InstancePropertiesView_ResolvePackageDependenciesOnText}"
                        OffContent="{x:Static lang:Resources.Enum_Off}"
                        IsChecked="{Binding BehaviorResolveDependency,Mode=TwoWay}" />
                </StackPanel>
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="[Experimental] Deployment Method">
                <StackPanel Spacing="8">
                    <ComboBox SelectedIndex="0" IsEnabled="False">
                        <ComboBoxItem Content="Auto" />
                    </ComboBox>
                </StackPanel>
            </controls:SettingsEntryItem>
            <controls:SettingsEntryItem Header="{x:Static lang:Resources.InstancePropertiesView_FastLaunchLabelText}">
                <StackPanel Spacing="8">
                    <husk:InfoBar
                        Content="{x:Static lang:Resources.InstancePropertiesView_FastLaunchPrompt}" />
                    <ToggleSwitch OnContent="{x:Static lang:Resources.InstancePropertiesView_FastLaunchOnText}"
                                  OffContent="{x:Static lang:Resources.Enum_Off}"
                                  IsChecked="{Binding BehaviorDeployFastMode,Mode=TwoWay}" />
                </StackPanel>
            </controls:SettingsEntryItem>
        </controls:SettingsEntry>
        <husk:Divider />
        <husk:BusyContainer
            Background="Transparent"
            IsBusy="{Binding State,Converter={x:Static husk:ObjectConverters.NotMatch},ConverterParameter=Idle,FallbackValue=True}">
            <controls:SettingsEntry Icon="Bug" Title="{x:Static lang:Resources.InstancePropertiesView_DebugTitle}"
                                    Summary="{x:Static lang:Resources.InstancePropertiesView_DebugSubtitle}">
                <controls:SettingsEntryItem
                    Header="{x:Static lang:Resources.InstancePropertiesView_CheckIntegrityLabelText}">
                    <Button Command="{Binding CheckIntegrityCommand}">
                        <TextBlock Text="{x:Static lang:Resources.InstancePropertiesView_CheckIntegrityButtonText}" />
                    </Button>
                </controls:SettingsEntryItem>
            </controls:SettingsEntry>
            <husk:BusyContainer.PendingContent>
                <husk:Card HorizontalAlignment="Center" VerticalAlignment="Center"
                           Background="{StaticResource OverlaySolidBackgroundBrush}">
                    <StackPanel Spacing="12">
                        <icons:PackIconLucide Kind="Info" Height="{StaticResource ExtraLargeFontSize}"
                                              Width="{StaticResource ExtraLargeFontSize}"
                                              HorizontalAlignment="Center" />
                        <TextBlock Text="{x:Static lang:Resources.InstancePropertiesView_InoperableLabelText}" />
                    </StackPanel>
                </husk:Card>
            </husk:BusyContainer.PendingContent>
        </husk:BusyContainer>
        <husk:Divider>
            <StackPanel Orientation="Horizontal" Spacing="8">
                <fi:SymbolIcon Symbol="Warning" FontSize="{StaticResource MediumFontSize}" />
                <TextBlock Text="猫脑过载" />
                <fi:SymbolIcon Symbol="Warning" FontSize="{StaticResource MediumFontSize}" />
            </StackPanel>
        </husk:Divider>
        <husk:BusyContainer
            Background="Transparent"
            IsBusy="{Binding State,Converter={x:Static husk:ObjectConverters.NotMatch},ConverterParameter=Idle,FallbackValue=True}">
            <controls:SettingsEntry Icon="WarningShield"
                                    Title="{x:Static lang:Resources.InstancePropertiesView_DangerZoneTitle}"
                                    Summary="{x:Static lang:Resources.InstancePropertiesView_DangerZoneSubtitle}">
                <components:SafeLock Name="Lock" SafeCode="{Binding SafeCode}" />
                <controls:SettingsEntryItem
                    Header="{x:Static lang:Resources.InstancePropertiesView_SacrificeLabelText}"
                    IsEnabled="{Binding #Lock.IsUnlocked }">
                    <Grid ColumnDefinitions="*,8,*">
                        <Button Grid.Column="0" Command="{Binding ResetInstanceCommand}">
                            <StackPanel Orientation="Horizontal" Spacing="8">
                                <fi:SymbolIcon Symbol="ArrowReset" FontSize="{StaticResource MediumFontSize}" />
                                <TextBlock Text="{x:Static lang:Resources.InstancePropertiesView_ResetButtonText}" />
                            </StackPanel>
                        </Button>
                        <Button Grid.Column="2" Classes="Danger" Command="{Binding DeleteInstanceCommand}">
                            <StackPanel Orientation="Horizontal" Spacing="8">
                                <fi:SymbolIcon Symbol="Delete" FontSize="{StaticResource MediumFontSize}" />
                                <TextBlock Text="{x:Static lang:Resources.InstancePropertiesView_DeleteButtonText}" />
                            </StackPanel>
                        </Button>
                    </Grid>
                </controls:SettingsEntryItem>
                <controls:SettingsEntryItem Header="{x:Static lang:Resources.InstancePropertiesView_LinkerLabelText}"
                                            IsEnabled="{Binding #Lock.IsUnlocked}"
                                            IsVisible="{Binding Basic.Source,Converter={x:Static ObjectConverters.IsNotNull}}">
                    <StackPanel Spacing="8">
                        <husk:InfoBar
                            Content="{x:Static lang:Resources.InstancePropertiesView_LinkerPrompt}" />
                        <Button Command="{Binding UnlockInstanceCommand}">
                            <StackPanel Orientation="Horizontal" Spacing="8">
                                <fi:SymbolIcon Symbol="WarningLockOpen" FontSize="{StaticResource MediumFontSize}" />
                                <TextBlock Text="{x:Static lang:Resources.InstancePropertiesView_UnlockButtonText}" />
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </controls:SettingsEntryItem>
            </controls:SettingsEntry>
            <husk:BusyContainer.PendingContent>
                <husk:Card HorizontalAlignment="Center" VerticalAlignment="Center"
                           Background="{StaticResource OverlaySolidBackgroundBrush}">
                    <StackPanel Spacing="12">
                        <icons:PackIconLucide Kind="Info" Height="{StaticResource ExtraLargeFontSize}"
                                              Width="{StaticResource ExtraLargeFontSize}"
                                              HorizontalAlignment="Center" />
                        <TextBlock Text="{x:Static lang:Resources.InstancePropertiesView_InoperableLabelText}" />
                    </StackPanel>
                </husk:Card>
            </husk:BusyContainer.PendingContent>
        </husk:BusyContainer>
    </StackPanel>
</controls:Subpage>
