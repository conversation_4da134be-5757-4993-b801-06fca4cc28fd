<controls:ScopedPage
    x:Class="Polymerium.App.Views.NewInstanceView"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="using:Polymerium.App.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:fi="using:FluentIcons.Avalonia"
    xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
    xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="using:Polymerium.App.ViewModels"
    xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
    Padding="0"
    HorizontalAlignment="Center"
    VerticalAlignment="Center"
    d:DesignHeight="600"
    d:DesignWidth="800"
    x:DataType="vm:NewInstanceViewModel"
    IsHeaderVisible="False"
    mc:Ignorable="d">
    <Grid ColumnDefinitions="Auto,*">
        <Border
            Grid.Column="0"
            Width="320"
            Margin="8"
            CornerRadius="{StaticResource MediumCornerRadius}">
            <Border.Background>
                <ImageBrush Source="/Assets/Images/Wallpaper.png" Stretch="UniformToFill" Opacity="0.9" />
            </Border.Background>
        </Border>
        <Grid Grid.Column="1" RowDefinitions="Auto,0,*" MinWidth="328">
            <Grid Grid.Row="0" Margin="12" ColumnDefinitions="Auto,12,*,12,Auto">
                <Button Grid.Column="0" Command="{Binding $parent[husk:Frame].GoBackCommand}">
                    <fi:SymbolIcon
                        Width="12"
                        Height="12"
                        FontSize="12"
                        Symbol="ArrowLeft" />
                </Button>
                <TextBlock
                    Grid.Column="2"
                    Text="{x:Static lang:Resources.NewInstanceView_Title}"
                    Theme="{StaticResource PageHeaderTextBlockTheme}" />
            </Grid>
            <StackPanel
                Grid.Row="2"
                Margin="24"
                VerticalAlignment="Center"
                Spacing="12">
                <Grid ColumnDefinitions="Auto,12,*">
                    <husk:DropZone Grid.Column="0"
                                   Width="108"
                                   Height="108"
                                   Padding="8"
                                   Background="{StaticResource LayerBackgroundBrush}"
                                   BorderBrush="{x:Null}"
                                   CornerRadius="{StaticResource MediumCornerRadius}"
                                   DragOver="DropZone_OnDragOver"
                                   Drop="DropZone_OnDrop"
                                   Model="{Binding Thumbnail, Mode=OneWayToSource}">
                        <husk:DropZone.Effect>
                            <DropShadowEffect Opacity="0.1" />
                        </husk:DropZone.Effect>
                        <Panel>
                            <Border Name="ThumbnailBox" CornerRadius="{StaticResource MediumCornerRadius}" />
                            <Panel Name="DropPanel" IsHitTestVisible="False">
                                <Panel.Transitions>
                                    <Transitions>
                                        <DoubleTransition Property="Opacity"
                                                          Duration="{StaticResource ControlFasterAnimationDuration}" />
                                    </Transitions>
                                </Panel.Transitions>
                                <Rectangle
                                    RadiusX="3"
                                    RadiusY="3"
                                    Stroke="{StaticResource ControlInteractiveBorderBrush}"
                                    StrokeDashArray="4,2"
                                    StrokeThickness="3" />

                                <icons:PackIconLucide
                                    Name="Icon"
                                    Margin="24"
                                    Foreground="{StaticResource ControlInteractiveBorderBrush}"
                                    Kind="Image" />
                            </Panel>
                        </Panel>
                        <husk:DropZone.Styles>
                            <Style Selector=":not(:dragover) Panel#DropPanel">
                                <Setter Property="Opacity" Value="{StaticResource Overlay1Opacity}" />
                            </Style>
                            <Style Selector=":dragover Panel#DropPanel">
                                <Setter Property="Opacity" Value="1" />
                            </Style>
                            <Style Selector=":drop Border#ThumbnailBox">
                                <Setter Property="Background">
                                    <ImageBrush Source="{Binding Thumbnail}" />
                                </Setter>
                            </Style>
                            <Style Selector=":drop icons|PackIconLucide#Icon">
                                <Setter Property="IsVisible" Value="False" />
                            </Style>
                        </husk:DropZone.Styles>
                    </husk:DropZone>
                    <StackPanel
                        Grid.Column="2"
                        VerticalAlignment="Center"
                        Spacing="12">
                        <StackPanel>
                            <TextBlock Text="{x:Static lang:Resources.NewInstanceView_NameLabelText}" />
                            <TextBox Text="{Binding DisplayName, Mode=TwoWay}" Watermark="{Binding VersionName}">
                                <TextBox.InnerLeftContent>
                                    <StackPanel Orientation="Horizontal">
                                        <icons:PackIconLucide
                                            Height="{StaticResource MediumFontSize}"
                                            Margin="10,0"
                                            VerticalAlignment="Center"
                                            Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                            Kind="Tag" />
                                        <husk:Divider Orientation="Vertical" />
                                    </StackPanel>
                                </TextBox.InnerLeftContent>
                            </TextBox>
                        </StackPanel>
                        <StackPanel>
                            <TextBlock Text="{x:Static lang:Resources.NewInstanceView_VersionLabelText}" />
                            <husk:SkeletonContainer CornerRadius="{StaticResource SmallCornerRadius}"
                                                    IsLoading="{Binding IsVersionLoaded, Converter={x:Static BoolConverters.Not}}">
                                <TextBox Text="{Binding VersionName, Mode=TwoWay}"
                                         IsEnabled="{Binding ImportedPack,Converter={x:Static ObjectConverters.IsNull}}">
                                    <TextBox.InnerLeftContent>
                                        <StackPanel Orientation="Horizontal">
                                            <icons:PackIconLucide
                                                Height="{StaticResource MediumFontSize}"
                                                Margin="10,0"
                                                VerticalAlignment="Center"
                                                Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                                Kind="Compass" />
                                            <husk:Divider Orientation="Vertical" />
                                        </StackPanel>
                                    </TextBox.InnerLeftContent>
                                    <TextBox.InnerRightContent>
                                        <Button Command="{Binding PickVersionCommand}">
                                            <icons:PackIconLucide Height="{StaticResource MediumFontSize}" Kind="List" />
                                        </Button>
                                    </TextBox.InnerRightContent>
                                </TextBox>
                            </husk:SkeletonContainer>
                        </StackPanel>
                    </StackPanel>
                </Grid>
                <Border
                    IsVisible="{Binding ImportedPack,Converter={x:Static ObjectConverters.IsNotNull},FallbackValue=True}"
                    Background="{StaticResource ControlTranslucentFullBackgroundBrush}" Padding="3"
                    CornerRadius="{StaticResource MediumCornerRadius}">
                    <Grid ColumnDefinitions="*,4,Auto">
                        <Button Grid.Column="2" Classes="Small" Command="{Binding ClearImportedPackCommand}"
                                Theme="{StaticResource GhostButtonTheme}">
                            <fi:SymbolIcon Symbol="Dismiss" FontSize="{StaticResource MediumFontSize}" />
                        </Button>
                        <Border Grid.Column="0" Background="{StaticResource OverlaySolidBackgroundBrush}" Padding="8"
                                CornerRadius="{StaticResource SmallCornerRadius}">
                            <Grid ColumnDefinitions="*,Auto,*" ColumnSpacing="8">
                                <Grid Grid.Column="0" ColumnDefinitions="Auto,*" ColumnSpacing="8">
                                    <Image Grid.Column="0" Source="/Assets/Icons/Package.png" Height="36" Width="36" />
                                    <StackPanel Grid.Column="1">
                                        <TextBlock
                                            Text="{x:Static lang:Resources.NewInstanceView_PackageCountLabelText}"
                                            Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                        <TextBlock Text="{Binding ImportedPack.PackageCount,FallbackValue=114514}"
                                                   FontSize="{StaticResource LargeFontSize}" />
                                    </StackPanel>
                                </Grid>
                                <husk:Divider Grid.Column="1" Orientation="Vertical" />
                                <StackPanel Grid.Column="2">
                                    <TextBlock Text="{x:Static lang:Resources.NewInstanceView_ModLoaderLabelText}"
                                               Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                    <TextBlock Text="{Binding ImportedPack.LoaderLabel,FallbackValue=None}"
                                               FontSize="{StaticResource LargeFontSize}" />
                                </StackPanel>
                            </Grid>
                        </Border>
                    </Grid>
                </Border>
                <Button
                    Height="42" Command="{Binding CreateCommand}"
                    Classes="Primary">
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <icons:PackIconLucide
                            Width="18"
                            Height="18"
                            Kind="PackagePlus" />
                        <TextBlock Text="{x:Static lang:Resources.NewInstanceView_CreateButtonText}"
                                   VerticalAlignment="Center" />
                    </StackPanel>
                </Button>
                <husk:Divider Content="{x:Static lang:Resources.NewInstanceView_SeparatorLabelText}" />
                <Grid ColumnDefinitions="*,12,*">
                    <Button
                        Grid.Column="0"
                        Height="42" Command="{Binding OpenImportDialogCommand}"
                        Theme="{StaticResource OutlineButtonTheme}">
                        <StackPanel Orientation="Horizontal" Spacing="8">
                            <icons:PackIconLucide
                                Width="18"
                                Height="18"
                                Kind="Import" />
                            <TextBlock Text="{x:Static lang:Resources.NewInstanceView_ImportButtonText}"
                                       VerticalAlignment="Center" />
                        </StackPanel>
                    </Button>
                    <Button
                        Grid.Column="2"
                        Command="{Binding GotoMarketplaceCommand}"
                        Height="42"
                        Theme="{StaticResource OutlineButtonTheme}">
                        <StackPanel Orientation="Horizontal" Spacing="8">
                            <icons:PackIconLucide
                                Width="18"
                                Height="18"
                                Kind="Store" />
                            <TextBlock Text="{x:Static lang:Resources.NewInstanceView_DownloadButtonText}"
                                       VerticalAlignment="Center" />
                        </StackPanel>
                    </Button>
                </Grid>
            </StackPanel>
        </Grid>
    </Grid>
</controls:ScopedPage>