<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:local="clr-namespace:Polymerium.App.Controls"
                    xmlns:m="clr-namespace:Polymerium.App.Models"
                    xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
                    xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages">
    <ControlTheme x:Key="{x:Type local:InstancePackageDependencyButton}"
                  TargetType="local:InstancePackageDependencyButton">
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="Padding" Value="12,10" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Background" Value="{StaticResource TransparentBrush}" />
        <Setter Property="BorderBrush" Value="{StaticResource TransparentBrush}" />
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}" />
        <Setter Property="Template">
            <ControlTemplate x:DataType="m:InstancePackageDependencyModel">
                <Panel Name="Container">
                    <Panel Margin="4,2">
                        <Border Name="Background"
                                CornerRadius="{TemplateBinding CornerRadius}"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}">
                            <Border.Transitions>
                                <Transitions>
                                    <BrushTransition Property="Background" Easing="SineEaseOut"
                                                     Duration="{StaticResource ControlFasterAnimationDuration}" />
                                    <BrushTransition Property="BorderBrush" Easing="SineEaseOut"
                                                     Duration="{StaticResource ControlFasterAnimationDuration}" />
                                </Transitions>
                            </Border.Transitions>
                        </Border>
                        <Grid Margin="{TemplateBinding Padding}" ColumnDefinitions="Auto,*,Auto"
                              ColumnSpacing="12">
                            <Border Grid.Column="0"
                                    CornerRadius="{StaticResource SmallCornerRadius}"
                                    Height="24" Width="24">
                                <Border.Background>
                                    <ImageBrush Source="{Binding Thumbnail}" />
                                </Border.Background>
                            </Border>
                            <TextBlock Grid.Column="1" VerticalAlignment="Center" TextTrimming="CharacterEllipsis"
                                       Text="{Binding ProjectName}" FontSize="{StaticResource LargeFontSize}" />
                            <StackPanel Grid.Column="2" VerticalAlignment="Center" Orientation="Horizontal" Spacing="4">
                                <husk:Tag
                                    Content="{x:Static lang:Resources.InstancePackageDependencyButton_RequiredTagText}"
                                    VerticalAlignment="Center" IsVisible="{Binding IsRequired}" />
                                <husk:Tag VerticalAlignment="Center">
                                    <TextBlock>
                                        <Run
                                            Text="{x:Static lang:Resources.InstancePackageDependencyButton_RefCountTagText}" />
                                        <Run Text="{Binding RefCount}" />
                                    </TextBlock>
                                </husk:Tag>
                            </StackPanel>
                        </Grid>
                        <Border Name="Overlay" CornerRadius="{TemplateBinding CornerRadius}"
                                Background="{StaticResource OverlayHalfBackgroundBrush}"
                                BorderBrush="{StaticResource ControlAccentInteractiveBorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}" Opacity="0">
                            <Border.Transitions>
                                <Transitions>
                                    <BrushTransition Property="Background" Easing="SineEaseOut"
                                                     Duration="{StaticResource ControlFasterAnimationDuration}" />
                                    <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                                      Duration="{StaticResource ControlFasterAnimationDuration}" />
                                </Transitions>
                            </Border.Transitions>
                        </Border>
                    </Panel>
                    <Border Name="Indicator" Width="3" Opacity="0" HorizontalAlignment="Left"
                            RenderTransform="translateX(-4px)" BorderThickness="1"
                            BorderBrush="{StaticResource ControlAccentTranslucentFullBackgroundBrush}"
                            Background="{StaticResource ControlAccentInteractiveBackgroundBrush}"
                            CornerRadius="{StaticResource SmallCornerRadius}" Margin="0,4"
                            IsVisible="{Binding Installed.IsEnabled,Mode=OneWay,FallbackValue={x:False}}">
                        <Border.Transitions>
                            <Transitions>
                                <DoubleTransition Property="Opacity" Easing="SineEaseOut"
                                                  Duration="{StaticResource ControlFasterAnimationDuration}" />
                                <TransformOperationsTransition Property="RenderTransform" Easing="SineEaseOut"
                                                               Duration="{StaticResource ControlFasterAnimationDuration}" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                </Panel>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover /template/ Border#Overlay">
            <Setter Property="Opacity"
                    Value="1" />
        </Style>
        <Style Selector="^:pressed /template/ Border#Overlay">
            <Setter Property="Background" Value="{StaticResource OverlayFullBackgroundBrush}" />
        </Style>
        <Style Selector="^[IsChecked=True] /template/ Border#Background">
            <Setter Property="Background" Value="{StaticResource ControlAccentTranslucentHalfBackgroundBrush}" />
        </Style>
        <Style Selector="^[IsChecked=True] /template/ Border#Indicator">
            <Setter Property="Opacity" Value="1" />
            <Setter Property="RenderTransform" Value="translateX(0px)" />
        </Style>
    </ControlTheme>
</ResourceDictionary>