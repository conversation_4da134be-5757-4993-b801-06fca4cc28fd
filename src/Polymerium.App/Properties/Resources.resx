<?xml version="1.0" encoding="utf-8"?>

<root>
    <xsd:schema xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata"
                id="root"
                xmlns="">
        <xsd:element name="root" msdata:IsDataSet="true">

        </xsd:element>
    </xsd:schema>
    <resheader name="resmimetype">
        <value>text/microsoft-resx</value>
    </resheader>
    <resheader name="version">
        <value>1.3</value>
    </resheader>
    <resheader name="reader">
        <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=2.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <resheader name="writer">
        <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=2.0.0.0, Culture=neutral,
            PublicKeyToken=b77a5c561934e089
        </value>
    </resheader>
    <data name="MainWindow_BackgroundText" xml:space="preserve">
        <value>B-b-b-bird, bird, bird, b-bird's the word...</value>
    </data>
    <data name="MainWindow_HomeButtonText" xml:space="preserve">
        <value>Home</value>
    </data>
    <data name="MainWindow_MarketplaceButtonText" xml:space="preserve">
        <value>Go to Marketplace</value>
    </data>
    <data name="MainWindow_InstanceFilterPlaceholder" xml:space="preserve">
        <value>Filter entries by names...</value>
    </data>
    <data name="MainWindow_AccountButtonText" xml:space="preserve">
        <value>Accounts</value>
    </data>
    <data name="AccountsView_AddAccountButtonText" xml:space="preserve">
        <value>Add Account</value>
    </data>
    <data name="AccountsView_Title" xml:space="preserve">
        <value>Accounts</value>
    </data>
    <data name="Shared_EmptyListLabelText" xml:space="preserve">
        <value>No Entries</value>
    </data>
    <data name="AccountsView_MarkAsDefaultMenuText" xml:space="preserve">
        <value>Mark as Default</value>
    </data>
    <data name="AccountsView_RemoveMenuText" xml:space="preserve">
        <value>Remove</value>
    </data>
    <data name="ExceptionView_Title" xml:space="preserve">
        <value>Died, but not dead...</value>
    </data>
    <data name="ExceptionView_Subtitle" xml:space="preserve">
        <value>It seems something went wrong...</value>
    </data>
    <data name="InstanceHomeView_OverviewTitle" xml:space="preserve">
        <value>Overview</value>
    </data>
    <data name="InstanceHomeView_PackageCountText" xml:space="preserve">
        <value>packages</value>
    </data>
    <data name="InstanceHomeView_SetupTitle" xml:space="preserve">
        <value>Setup</value>
    </data>
    <data name="InstanceHomeView_SetupTypeText" xml:space="preserve">
        <value>Type</value>
    </data>
    <data name="InstanceHomeView_SetupVersionText" xml:space="preserve">
        <value>Version</value>
    </data>
    <data name="InstanceHomeView_ActivitiesTitle" xml:space="preserve">
        <value>Activities</value>
    </data>
    <data name="InstanceHomeView_HourCountText" xml:space="preserve">
        <value>Total Hours</value>
    </data>
    <data name="InstanceHomeView_WidgetsTitle" xml:space="preserve">
        <value>Widgets</value>
    </data>
    <data name="InstanceHomeView_LaunchPadTitle" xml:space="preserve">
        <value>Launch Pad</value>
    </data>
    <data name="InstanceHomeView_SeeMoreButtonText" xml:space="preserve">
        <value>See More</value>
    </data>
    <data name="InstanceHomeView_SetupEditButtonText" xml:space="preserve">
        <value>Details</value>
    </data>
    <data name="InstanceHomeView_ReadyLabelText" xml:space="preserve">
        <value>Everything is ready</value>
    </data>
    <data name="InstanceHomeView_NoAccountLabelText" xml:space="preserve">
        <value>Account is not set yet</value>
    </data>
    <data name="InstanceHomeView_LaunchButtonText" xml:space="preserve">
        <value>LAUNCH</value>
    </data>
    <data name="InstanceHomeView_AccountButtonTitle" xml:space="preserve">
        <value>Account</value>
    </data>
    <data name="InstanceHomeView_AccountButtonSubtitle" xml:space="preserve">
        <value>Unchosen</value>
    </data>
    <data name="InstanceHomeView_AbortButtonText" xml:space="preserve">
        <value>ABORT</value>
    </data>
    <data name="InstanceHomeView_KillButtonText" xml:space="preserve">
        <value>KILL</value>
    </data>
    <data name="InstanceHomeView_DetachButtonText" xml:space="preserve">
        <value>Eject</value>
    </data>
    <data name="InstanceHomeView_DashboardButtonTitle" xml:space="preserve">
        <value>Logs</value>
    </data>
    <data name="InstanceHomeView_DashboardButtonSubtitle" xml:space="preserve">
        <value>Dashboard</value>
    </data>
    <data name="InstancePropertiesView_TitleTitle" xml:space="preserve">
        <value>Title</value>
    </data>
    <data name="InstancePropertiesView_TitleSubtitle" xml:space="preserve">
        <value>How this instance identify itself</value>
    </data>
    <data name="InstancePropertiesView_DisplayNameLabelText" xml:space="preserve">
        <value>Display Name</value>
    </data>
    <data name="InstancePropertiesView_RenameButtonText" xml:space="preserve">
        <value>Rename</value>
    </data>
    <data name="InstancePropertiesView_SelectButtonText" xml:space="preserve">
        <value>Select</value>
    </data>
    <data name="InstancePropertiesView_RemoveButtonText" xml:space="preserve">
        <value>Remove</value>
    </data>
    <data name="InstancePropertiesView_OverridesTitle" xml:space="preserve">
        <value>Game Overrides</value>
    </data>
    <data name="InstancePropertiesView_OverridesSubtitle" xml:space="preserve">
        <value>Configuration specified to the instance; left blank to refer to the global settings</value>
    </data>
    <data name="InstancePropertiesView_NavigateToGlobalButtonTitle" xml:space="preserve">
        <value>Navigate to the global settings page</value>
    </data>
    <data name="InstancePropertiesView_NavigateToGlobalButtonSubtitle" xml:space="preserve">
        <value>Entries here will overwrite the global settings</value>
    </data>
    <data name="InstancePropertiesView_JavaLabelText" xml:space="preserve">
        <value>Java Home</value>
    </data>
    <data name="InstancePropertiesView_JavaMaxMemoryLabelText" xml:space="preserve">
        <value>Java Max Memory</value>
    </data>
    <data name="InstancePropertiesView_JavaMaxMemoryUnitText" xml:space="preserve">
        <value>MiB</value>
    </data>
    <data name="InstancePropertiesView_JavaAdditionalArgumentsLabelText" xml:space="preserve">
        <value>Java Additional Arguments</value>
    </data>
    <data name="InstancePropertiesView_WindowInitialSizeLabelText" xml:space="preserve">
        <value>Window Initial Size</value>
    </data>
    <data name="InstancePropertiesView_WindowWidthLabelText" xml:space="preserve">
        <value>Width</value>
    </data>
    <data name="InstancePropertiesView_WindowHeightLabelText" xml:space="preserve">
        <value>Height</value>
    </data>
    <data name="InstancePropertiesView_BehaviorsTitle" xml:space="preserve">
        <value>Behaviors</value>
    </data>
    <data name="InstancePropertiesView_BehaviorsSubtitle" xml:space="preserve">
        <value>How the application acts and chooses strategies</value>
    </data>
    <data name="InstancePropertiesView_ResolvePackageDependenciesLabelText" xml:space="preserve">
        <value>[Experimental] Resolve Package Dependencies</value>
    </data>
    <data name="InstancePropertiesView_ResolvePackageDependenciesPrompt" xml:space="preserve">
        <value>This always leads to unexpected package conflicts due to poor management of package distribution sites. </value>
    </data>
    <data name="InstancePropertiesView_FastLaunchLabelText" xml:space="preserve">
        <value>Fast Launch</value>
    </data>
    <data name="InstancePropertiesView_FastLaunchPrompt" xml:space="preserve">
        <value>Skip the asset and file validation process if the instance is already built and launch the game directly. May lead to crash if the assets are corrupted.</value>
    </data>
    <data name="InstancePropertiesView_DangerZoneTitle" xml:space="preserve">
        <value>Danger Zone</value>
    </data>
    <data name="InstancePropertiesView_DangerZoneSubtitle" xml:space="preserve">
        <value>These settings are applied instantly and can't be recovered</value>
    </data>
    <data name="InstancePropertiesView_SacrificeLabelText" xml:space="preserve">
        <value>Scrifice</value>
    </data>
    <data name="InstancePropertiesView_UnlockButtonText" xml:space="preserve">
        <value>Unlock</value>
    </data>
    <data name="InstancePropertiesView_ResetButtonText" xml:space="preserve">
        <value>Reset</value>
    </data>
    <data name="InstancePropertiesView_DeleteButtonText" xml:space="preserve">
        <value>Delete</value>
    </data>
    <data name="InstancePropertiesView_LinkerLabelText" xml:space="preserve">
        <value>Link</value>
    </data>
    <data name="InstancePropertiesView_LinkerPrompt" xml:space="preserve">
        <value>Unlock by removing the attached pack brand/metadata that will make the instance losing the ability of updating but gaining the ability of editing all the imported packages.</value>
    </data>
    <data name="InstancePropertiesView_InoperableLabelText" xml:space="preserve">
        <value>The instance is busy now</value>
    </data>
    <data name="InstanceSetupView_InoperableLabelText" xml:space="preserve">
        <value>Operating In Progress</value>
    </data>
    <data name="InstanceSetupView_VersionLabelText" xml:space="preserve">
        <value>Game Version</value>
    </data>
    <data name="InstanceSetupView_LoaderLabelText" xml:space="preserve">
        <value>Mod Loader</value>
    </data>
    <data name="InstanceSetupView_ReferenceUnavailableLabelText" xml:space="preserve">
        <value>Something went wrong</value>
    </data>
    <data name="Shared_ExternalLinkLabelText" xml:space="preserve">
        <value>EXTERNAL LINK TO</value>
    </data>
    <data name="InstanceSetupView_SwitchVersionButtonText" xml:space="preserve">
        <value>Switch Version</value>
    </data>
    <data name="InstanceSetupView_LoadingPackageLabelText" xml:space="preserve">
        <value>Loading packages information...</value>
    </data>
    <data name="MarketplacePortalView_Title" xml:space="preserve">
        <value>Marketplace Portal</value>
    </data>
    <data name="MarketplacePortalView_NewsLearnMoreButtonText" xml:space="preserve">
        <value>Learn More</value>
    </data>
    <data name="MarketplacePortalView_DiscoveryCenterTitle" xml:space="preserve">
        <value>Discovery Center</value>
    </data>
    <data name="MarketplacePortalView_DiscoveryCenterSubtitle" xml:space="preserve">
        <value>Everything(mostly) begins here</value>
    </data>
    <data name="MarketplacePortalView_SearchButtonText" xml:space="preserve">
        <value>Search</value>
    </data>
    <data name="MarketplacePortalView_NewsLabelText" xml:space="preserve">
        <value>News</value>
    </data>
    <data name="MarketplaceSearchView_Title" xml:space="preserve">
        <value>Search modpacks</value>
    </data>
    <data name="MarketplaceSearchView_SearchBarPlaceholder" xml:space="preserve">
        <value>With the keywords in...</value>
    </data>
    <data name="MarketplaceSearchView_SearchButtonText" xml:space="preserve">
        <value>Search</value>
    </data>
    <data name="MarketplaceSearchView_ResultCountLabelText" xml:space="preserve">
        <value>Results:</value>
    </data>
    <data name="MarketplaceSearchView_ResetFilterButtonText" xml:space="preserve">
        <value>Clear Filter</value>
    </data>
    <data name="Shared_FetchingLabelText" xml:space="preserve">
        <value>Fetching...</value>
    </data>
    <data name="NewInstanceView_CreateButtonText" xml:space="preserve">
        <value>Create instance</value>
    </data>
    <data name="NewInstanceView_Title" xml:space="preserve">
        <value>Create an instance</value>
    </data>
    <data name="NewInstanceView_NameLabelText" xml:space="preserve">
        <value>Name</value>
    </data>
    <data name="NewInstanceView_VersionLabelText" xml:space="preserve">
        <value>Version</value>
    </data>
    <data name="NewInstanceView_PackageCountLabelText" xml:space="preserve">
        <value>Packages</value>
    </data>
    <data name="NewInstanceView_ModLoaderLabelText" xml:space="preserve">
        <value>Mod Loader</value>
    </data>
    <data name="NewInstanceView_SeparatorLabelText" xml:space="preserve">
        <value>Or create from</value>
    </data>
    <data name="NewInstanceView_ImportButtonText" xml:space="preserve">
        <value>Import</value>
    </data>
    <data name="NewInstanceView_DownloadButtonText" xml:space="preserve">
        <value>Marketplace</value>
    </data>
    <data name="PackageExplorerView_PendingLabelText" xml:space="preserve">
        <value>Pending</value>
    </data>
    <data name="PackageExplorerView_AddLabelText" xml:space="preserve">
        <value>Add</value>
    </data>
    <data name="PackageExplorerView_ModifyLabelText" xml:space="preserve">
        <value>Modify</value>
    </data>
    <data name="PackageExplorerView_RemoveLabelText" xml:space="preserve">
        <value>Remove</value>
    </data>
    <data name="PackageExplorerView_EmptyLabelText" xml:space="preserve">
        <value>Empty</value>
    </data>
    <data name="PackageExplorerView_CollectButtonText" xml:space="preserve">
        <value>Collect</value>
    </data>
    <data name="PackageExplorerView_DismissButtonText" xml:space="preserve">
        <value>Dismiss</value>
    </data>
    <data name="PackageExplorerView_SearchBarPlaceholder" xml:space="preserve">
        <value>Search by names and names...</value>
    </data>
    <data name="PackageExplorerView_SearchButtonText" xml:space="preserve">
        <value>Search</value>
    </data>
    <data name="SettingsView_Title" xml:space="preserve">
        <value>Application Settings</value>
    </data>
    <data name="SettingsView_SuperPowerTitle" xml:space="preserve">
        <value>Super Power</value>
    </data>
    <data name="SettingsView_SuperPowerSubtitle" xml:space="preserve">
        <value>...</value>
    </data>
    <data name="SettingsView_SuperPowerLabelText" xml:space="preserve">
        <value>Activate</value>
    </data>
    <data name="SettingsView_DisplayTitle" xml:space="preserve">
        <value>Display and Appearance</value>
    </data>
    <data name="SettingsView_DisplaySubtitle" xml:space="preserve">
        <value>The look, the language, the font</value>
    </data>
    <data name="SettingsView_SidebarPlacementLabelText" xml:space="preserve">
        <value>Sidebar Placement</value>
    </data>
    <data name="SettingsView_SidebarPlacementLeftText" xml:space="preserve">
        <value>Left</value>
    </data>
    <data name="SettingsView_SidebarPlacementRightText" xml:space="preserve">
        <value>Right</value>
    </data>
    <data name="SettingsView_ThemeVariantLabelText" xml:space="preserve">
        <value>Theme Variant</value>
    </data>
    <data name="SettingsView_ThemeVariantSystemText" xml:space="preserve">
        <value>System</value>
    </data>
    <data name="SettingsView_ThemeVariantLightText" xml:space="preserve">
        <value>Light</value>
    </data>
    <data name="SettingsView_ThemeVariantDarkText" xml:space="preserve">
        <value>Dark</value>
    </data>
    <data name="SettingsView_BackgroundStyleLabelText" xml:space="preserve">
        <value>Background Style</value>
    </data>
    <data name="SettingsView_BackgroundStyleAutoText" xml:space="preserve">
        <value>Auto</value>
    </data>
    <data name="SettingsView_BackgroundStyleMicaText" xml:space="preserve">
        <value>Mica</value>
    </data>
    <data name="SettingsView_BackgroundStyleAcrylicText" xml:space="preserve">
        <value>Acrylic</value>
    </data>
    <data name="SettingsView_BackgroundStyleBlurText" xml:space="preserve">
        <value>Blur</value>
    </data>
    <data name="SettingsView_BackgroundStyleNoneText" xml:space="preserve">
        <value>None</value>
    </data>
    <data name="SettingsView_LanguageLabelText" xml:space="preserve">
        <value>Language</value>
    </data>
    <data name="SettingsView_FontLabelText" xml:space="preserve">
        <value>Font</value>
    </data>
    <data name="SettingsView_JavaTitle" xml:space="preserve">
        <value>Java Presents</value>
    </data>
    <data name="SettingsView_JavaSubtitle" xml:space="preserve">
        <value>Instance will select the best match while launching</value>
    </data>
    <data name="SettingsView_Java8LabelText" xml:space="preserve">
        <value>Java 8 Home</value>
    </data>
    <data name="SettingsView_Java11LabelText" xml:space="preserve">
        <value>Java 11 Home</value>
    </data>
    <data name="SettingsView_Java17LabelText" xml:space="preserve">
        <value>Java 17 Home</value>
    </data>
    <data name="SettingsView_Java21LabelText" xml:space="preserve">
        <value>Java 21 Home</value>
    </data>
    <data name="SettingsView_GameDefaultsTitle" xml:space="preserve">
        <value>Game Defaults</value>
    </data>
    <data name="SettingsView_GameDefaultsSubtitle" xml:space="preserve">
        <value>The listing configuration can be override by the instance individually</value>
    </data>
    <data name="SettingsView_JavaMaxMemoryLabelText" xml:space="preserve">
        <value>Java Max Memory</value>
    </data>
    <data name="SettingsView_JavaMaxMemoryPlaceholder" xml:space="preserve">
        <value>Bigger is better</value>
    </data>
    <data name="SettingsView_JavaAdditionalArgumentsLabelText" xml:space="preserve">
        <value>Java Additional Arguments</value>
    </data>
    <data name="SettingsView_JavaAdditionalArgumentsPlaceholder" xml:space="preserve">
        <value>Less is more</value>
    </data>
    <data name="SettingsView_WindowInitialSizeLabelText" xml:space="preserve">
        <value>Window Initial Size</value>
    </data>
    <data name="SettingsView_JavaMaxMemoryUnitText" xml:space="preserve">
        <value>MiB</value>
    </data>
    <data name="SettingsView_WindowHeightLabelText" xml:space="preserve">
        <value>Height</value>
    </data>
    <data name="SettingsView_WindowWidthLabelText" xml:space="preserve">
        <value>Width</value>
    </data>
    <data name="AccountCreationMicrosoft_Title" xml:space="preserve">
        <value>Microsoft Authentication</value>
    </data>
    <data name="AccountCreationMicrosoft_UnavailableLabelText" xml:space="preserve">
        <value>Something went wrong...</value>
    </data>
    <data name="AccountCreationMicrosoft_RetryButtonText" xml:space="preserve">
        <value>Retry</value>
    </data>
    <data name="AccountCreationMicrosoft_DoneTitle" xml:space="preserve">
        <value>Done!</value>
    </data>
    <data name="AccountCreationMicrosoft_DoneSubtitle" xml:space="preserve">
        <value>The account has been linked. Checkout in the next page.</value>
    </data>
    <data name="AccountCreationMicrosoft_OpenLinkButtonText" xml:space="preserve">
        <value>Open in the browser</value>
    </data>
    <data name="AccountCreationMicrosoft_Prompt" xml:space="preserve">
        <value>The code will be expired in 15 minutes. Please fill this code in the page:</value>
    </data>
    <data name="AccountCreationOffline_Title" xml:space="preserve">
        <value>Pick a name</value>
    </data>
    <data name="AccountCreationOffline_NameLabelText" xml:space="preserve">
        <value>Name</value>
    </data>
    <data name="AccountCreationOffline_UuidLabelText" xml:space="preserve">
        <value>UUID</value>
    </data>
    <data name="AccountCreationOffline_Prompt" xml:space="preserve">
        <value>Name containing spaces or other non-ascii characters may cause issues.</value>
    </data>
    <data name="AccountCreationPortal_Title" xml:space="preserve">
        <value>Pick an account type</value>
    </data>
    <data name="AccountCreationPortal_MicrosoftTitle" xml:space="preserve">
        <value>Microsoft</value>
    </data>
    <data name="AccountCreationPortal_MicrosoftSubtitle" xml:space="preserve">
        <value>Enables all the online features and is the only way to play the game legally.</value>
    </data>
    <data name="AccountCreationPortal_TrialTitle" xml:space="preserve">
        <value>Trial</value>
    </data>
    <data name="AccountCreationPortal_TrialSubtitle" xml:space="preserve">
        <value>Play as the preset character if dont have an account.</value>
    </data>
    <data name="AccountCreationPortal_OfflineTitle" xml:space="preserve">
        <value>Offline</value>
    </data>
    <data name="AccountCreationPortal_OfflineSubtitle" xml:space="preserve">
        <value>Nothing good but no Internet connection required.</value>
    </data>
    <data name="AccountCreationPortal_Prompt" xml:space="preserve">
        <value>Offline account is unavailable if no Microsoft account is linked.</value>
    </data>
    <data name="AccountCreationPreview_Title" xml:space="preserve">
        <value>Account Preview</value>
    </data>
    <data name="AccountCreationPreview_Subtitle" xml:space="preserve">
        <value>🎉It's nearly done🎉</value>
    </data>
    <data name="AccountCreationTrial_Title" xml:space="preserve">
        <value>Pick your favorite family guy</value>
    </data>
    <data name="ExhibitStatePresenter_AddingTagText" xml:space="preserve">
        <value>Adding</value>
    </data>
    <data name="ExhibitStatePresenter_EditableTagText" xml:space="preserve">
        <value>Installed</value>
    </data>
    <data name="ExhibitStatePresenter_LockedTagText" xml:space="preserve">
        <value>Locked</value>
    </data>
    <data name="ExhibitStatePresenter_RemovingTagText" xml:space="preserve">
        <value>Removing</value>
    </data>
    <data name="ExhibitStatePresenter_ModifyingTagText" xml:space="preserve">
        <value>Modifying</value>
    </data>
    <data name="JavaHomeContainer_BrowseButtonText" xml:space="preserve">
        <value>Browse</value>
    </data>
    <data name="JavaHomeContainer_DetectButtonText" xml:space="preserve">
        <value>Detect</value>
    </data>
    <data name="JavaHomeContainer_UnknownLabelText" xml:space="preserve">
        <value>Unknown</value>
    </data>
    <data name="JavaHomeContainer_Prompt" xml:space="preserve">
        <value>Bundled or</value>
    </data>
    <data name="PackageContainer_FilterBarPlaceholder" xml:space="preserve">
        <value>Enter filter string...</value>
    </data>
    <data name="PackageContainer_ConditionLabelText" xml:space="preserve">
        <value>Filter</value>
    </data>
    <data name="PackageContainer_TagLabelText" xml:space="preserve">
        <value>Tags</value>
    </data>
    <data name="ResourceKind_Modpack" xml:space="preserve">
        <value>Modpack</value>
    </data>
    <data name="ResourceKind_Mod" xml:space="preserve">
        <value>Mod</value>
    </data>
    <data name="ResourceKind_ResourcePack" xml:space="preserve">
        <value>Resource Pack</value>
    </data>
    <data name="ResourceKind_ShaderPack" xml:space="preserve">
        <value>Shader Pack</value>
    </data>
    <data name="ResourceKind_DataPack" xml:space="preserve">
        <value>Data Pack</value>
    </data>
    <data name="Enum_All" xml:space="preserve">
        <value>All</value>
    </data>
    <data name="Enum_None" xml:space="preserve">
        <value>None</value>
    </data>
    <data name="Enum_Enabled" xml:space="preserve">
        <value>Enabled</value>
    </data>
    <data name="Enum_Disabled" xml:space="preserve">
        <value>Disabled</value>
    </data>
    <data name="PackageContainer_SourceOriginalText" xml:space="preserve">
        <value>Original</value>
    </data>
    <data name="PackageContainer_SourceLocalText" xml:space="preserve">
        <value>Local</value>
    </data>
    <data name="PackageContainer_SourceLabelText" xml:space="preserve">
        <value>Source</value>
    </data>
    <data name="PackageContainer_TypeLabelText" xml:space="preserve">
        <value>Type</value>
    </data>
    <data name="PackageContainer_ResultCountLabelText" xml:space="preserve">
        <value>Results</value>
    </data>
    <data name="PackageContainer_BatchUpdateMenuText" xml:space="preserve">
        <value>Batch Update</value>
    </data>
    <data name="PackageContainer_GetMoreButtonText" xml:space="preserve">
        <value>Get More</value>
    </data>
    <data name="PackageContainer_ExportListMenuText" xml:space="preserve">
        <value>Explort List</value>
    </data>
    <data name="SafeLock_Title" xml:space="preserve">
        <value>Safe Lock</value>
    </data>
    <data name="SafeLock_CodeLabelText" xml:space="preserve">
        <value>Code:</value>
    </data>
    <data name="SafeLock_RepeatLabelText" xml:space="preserve">
        <value>Repeat</value>
    </data>
    <data name="ExhibitDependencyButton_RequiredTagText" xml:space="preserve">
        <value>Required</value>
    </data>
    <data name="MarketplaceSearchView_OpenWebsiteMenuText" xml:space="preserve">
        <value>Open Website</value>
    </data>
    <data name="ExhibitModpackButton_InstallButtonText" xml:space="preserve">
        <value>Install</value>
    </data>
    <data name="InstanceEntryButton_InstallTagText" xml:space="preserve">
        <value>INSTALLING</value>
    </data>
    <data name="InstanceEntryButton_PreparingTagText" xml:space="preserve">
        <value>PREPARING</value>
    </data>
    <data name="InstanceEntryButton_UpdatingTagText" xml:space="preserve">
        <value>UPDATING</value>
    </data>
    <data name="InstanceEntryButton_RunningTagText" xml:space="preserve">
        <value>RUNNING</value>
    </data>
    <data name="PackageContainer_ActiveMenuText" xml:space="preserve">
        <value>Active</value>
    </data>
    <data name="PackageContainer_OpenWebsiteMenuText" xml:space="preserve">
        <value>Open Website</value>
    </data>
    <data name="PackageContainer_RemoveMenuText" xml:space="preserve">
        <value>Remove</value>
    </data>
    <data name="InstancePackageButton_DisabledLabelText" xml:space="preserve">
        <value>Disabled</value>
    </data>
    <data name="InstancePackageButton_OriginalTagText" xml:space="preserve">
        <value>Original</value>
    </data>
    <data name="InstancePackageButton_AutoVersionTagText" xml:space="preserve">
        <value>Auto Version</value>
    </data>
    <data name="AccountPickerDialog_Title" xml:space="preserve">
        <value>Choose a game account</value>
    </data>
    <data name="AccountPickerDialog_EmptyListPrompt" xml:space="preserve">
        <value>No account present in the vault</value>
    </data>
    <data name="AccountPickerDialog_ManageAccountsButtonText" xml:space="preserve">
        <value>Manage Accounts</value>
    </data>
    <data name="ExportPackageListDialog_Title" xml:space="preserve">
        <value>Export package list</value>
    </data>
    <data name="ExportPackageListDialog_Prompt" xml:space="preserve">
        <value>Export package list of the instance into the selected formatted table file</value>
    </data>
    <data name="ExportPackageListDialog_PackageCountLabelText" xml:space="preserve">
        <value>packages</value>
    </data>
    <data name="ExportPackageListDialog_PathLabelText" xml:space="preserve">
        <value>Export to</value>
    </data>
    <data name="ExportPackageListDialog_PathBarPlaceholder" xml:space="preserve">
        <value>Chooooosen file path</value>
    </data>
    <data name="FilePickerDialog_Title" xml:space="preserve">
        <value>Pick a file</value>
    </data>
    <data name="FilePickerDialog_DropZonePrompt" xml:space="preserve">
        <value>Drag and drop, or</value>
    </data>
    <data name="FilePickerDialog_BrowseButtonText" xml:space="preserve">
        <value>Browser files...</value>
    </data>
    <data name="FilePickerDialog_AlertPrompt" xml:space="preserve">
        <value>File too big makes app crash</value>
    </data>
    <data name="FilePickerDialog_PathBarPlaceholder" xml:space="preserve">
        <value>Chooooosen file path"</value>
    </data>
    <data name="GameVersionPickerDialog_Title" xml:space="preserve">
        <value>Pick a game version</value>
    </data>
    <data name="GameVersionPickerDialog_VersionBarPlaceholder" xml:space="preserve">
        <value>Filter by version names...</value>
    </data>
    <data name="ReferenceVersionPickerDialog_Title" xml:space="preserve">
        <value>Choose a version to upgrade/downgrade to</value>
    </data>
    <data name="ReferenceVersionPickerDialog_Prompt" xml:space="preserve">
        <value>All the mods will be replaced. Configs will be replaced with new ones.</value>
    </data>
    <data name="ReleaseType_Release" xml:space="preserve">
        <value>Release</value>
    </data>
    <data name="ReleaseType_Beta" xml:space="preserve">
        <value>Beta</value>
    </data>
    <data name="ReleaseType_Alpha" xml:space="preserve">
        <value>Alpha</value>
    </data>
    <data name="UserInputDialog_Title" xml:space="preserve">
        <value>Get something entered</value>
    </data>
    <data name="UserInputDialog_Prompt" xml:space="preserve">
        <value>Just write something down below...</value>
    </data>
    <data name="AccountCreationModal_NextButtonText" xml:space="preserve">
        <value>Next</value>
    </data>
    <data name="AccountCreationModal_FinishButtonText" xml:space="preserve">
        <value>Finish</value>
    </data>
    <data name="AccountCreationModal_BackButtonText" xml:space="preserve">
        <value>Back</value>
    </data>
    <data name="AccountCreationModal_DismissButtonText" xml:space="preserve">
        <value>Dismiss</value>
    </data>
    <data name="AccountEntryModal_LastUsedLabelText" xml:space="preserve">
        <value>Last Used</value>
    </data>
    <data name="AccountEntryModal_EnrolledLabelText" xml:space="preserve">
        <value>Enrolled</value>
    </data>
    <data name="AccountEntryModal_OfflinePrompt" xml:space="preserve">
        <value>It's all set.</value>
    </data>
    <data name="AccountEntryModal_TrialPrompt" xml:space="preserve">
        <value>It's all set.</value>
    </data>
    <data name="AccountEntryModal_MicrosoftPrompt" xml:space="preserve">
        <value>This kind of account is kind of: No use in the past then no use in the future any more.</value>
    </data>
    <data name="ExhibitPackageModal_FilterLabelText" xml:space="preserve">
        <value>Show compatible versions only</value>
    </data>
    <data name="ExhibitPackageModal_VersionBoxPlaceholder" xml:space="preserve">
        <value>Specify version...</value>
    </data>
    <data name="ExhibitPackageModal_VersionLabelText" xml:space="preserve">
        <value>Version:</value>
    </data>
    <data name="ExhibitPackageModal_AddButtonText" xml:space="preserve">
        <value>Add to the instance</value>
    </data>
    <data name="ExhibitPackageModal_InstalledVersionTagText" xml:space="preserve">
        <value>Installed:</value>
    </data>
    <data name="ExhibitPackageModal_UnspecifiedVersionTagText" xml:space="preserve">
        <value>Unspecified</value>
    </data>
    <data name="ExhibitPackageModal_ModifyButtonText" xml:space="preserve">
        <value>Apply changes</value>
    </data>
    <data name="ExhibitPackageModal_LockedVersionLabelText" xml:space="preserve">
        <value>Locked Version</value>
    </data>
    <data name="ExhibitPackageModal_AddingVersionTagText" xml:space="preserve">
        <value>Adding:</value>
    </data>
    <data name="ExhibitPackageModal_ModifyingTagText" xml:space="preserve">
        <value>Replacing:</value>
    </data>
    <data name="ExhibitPackageModal_RemovingTagText" xml:space="preserve">
        <value>Removing:</value>
    </data>
    <data name="ExhibitPackageModal_RestoreButtonText" xml:space="preserve">
        <value>Restore</value>
    </data>
    <data name="ExhibitPackageModal_AboutTabText" xml:space="preserve">
        <value>About</value>
    </data>
    <data name="ExhibitPackageModal_DependenciesTabText" xml:space="preserve">
        <value>Dependencies</value>
    </data>
    <data name="ExhibitPackageModal_DependenciesTabPrompt" xml:space="preserve">
        <value>Showing dependencies of version:</value>
    </data>
    <data name="ExhibitPackageModal_ChangelogsTabText" xml:space="preserve">
        <value>Changlogs</value>
    </data>
    <data name="ExhibitPackageModal_ChangelogsTabPrompt" xml:space="preserve">
        <value>Showing changelog of version:</value>
    </data>
    <data name="ExhibitPackageModal_EmptyListLabelText" xml:space="preserve">
        <value>Select a Version</value>
    </data>
    <data name="InstancePackageModal_VersionsTabText" xml:space="preserve">
        <value>Versions</value>
    </data>
    <data name="InstancePackageModal_VersionBoxUnspecificTitle" xml:space="preserve">
        <value>Auto Select Version</value>
    </data>
    <data name="InstancePackageModal_VersionBoxUnspecificSubtitle" xml:space="preserve">
        <value>Or pick a version in the list below.</value>
    </data>
    <data name="InstancePackageModal_VersionBoxLabelText" xml:space="preserve">
        <value>Current Version</value>
    </data>
    <data name="InstancePackageModal_LockedVersionLabelText" xml:space="preserve">
        <value>Not Editable</value>
    </data>
    <data name="InstancePackageModal_FilterLabelText" xml:space="preserve">
        <value>Show compatible versions only</value>
    </data>
    <data name="InstancePackageModal_TagsTabText" xml:space="preserve">
        <value>Tags</value>
    </data>
    <data name="InstancePackageModal_AddTagButtonText" xml:space="preserve">
        <value>Add</value>
    </data>
    <data name="Dialog_ConfirmButtonText" xml:space="preserve">
        <value>Confirm</value>
    </data>
    <data name="Dialog_CancelButtonText" xml:space="preserve">
        <value>Cancel</value>
    </data>
    <data name="Dialog_DismissButtonText" xml:space="preserve">
        <value>Dismiss</value>
    </data>
    <data name="ExhibitModpackToast_InstallButtonText" xml:space="preserve">
        <value>Install</value>
    </data>
    <data name="JavaHomeContainer_ReqeustJavaTitle" xml:space="preserve">
        <value>Select a Java executable</value>
    </data>
    <data name="JavaHomeContainer_RequestJavaPrompt" xml:space="preserve">
        <value>Pick a file like /bin/java.exe or /bin/javaw.exe</value>
    </data>
    <data name="NewInstanceView_RequestFileTitle" xml:space="preserve">
        <value>Import from file</value>
    </data>
    <data name="NewInstanceView_RequestFilePrompt" xml:space="preserve">
        <value>Select a compressed modpack file to import</value>
    </data>
    <data name="NewInstanceView_ImportDangerNotificationTitle" xml:space="preserve">
        <value>Import failed</value>
    </data>
    <data name="NewInstanceView_IconSavingDangerNotificationTitle" xml:space="preserve">
        <value>Write icon failed</value>
    </data>
    <data name="AccountsView_AccountAddingDangerNotificationTitle" xml:space="preserve">
        <value>Account creation failed</value>
    </data>
    <data name="AccountsView_AccountAddingDangerNotificationPrompt" xml:space="preserve">
        <value>Account with the same uuid already exists</value>
    </data>
    <data name="InstanceHomeView_AccountAuthenticationDangerNotificationTitle" xml:space="preserve">
        <value>Account authentication failed</value>
    </data>
    <data name="InstanceHomeView_DeployDangerNotificationTitle" xml:space="preserve">
        <value>Deployment failed</value>
    </data>
    <data name="InstanceHomeView_AccountNotFoundDangerNotificationTitle" xml:space="preserve">
        <value>Account Not Found</value>
    </data>
    <data name="InstanceHomeView_AccountNotFoundDangerNotificationPrompt" xml:space="preserve">
        <value>Account is not provided or removed after set</value>
    </data>
    <data name="InstanceHomeView_AccountNotFoundDangerNotificationSelectActionText" xml:space="preserve">
        <value>Select Account</value>
    </data>
    <data name="InstancePropertiesView_ThumbnailSavingDangerNotificationTitle" xml:space="preserve">
        <value>Thumbnail saving failed</value>
    </data>
    <data name="InstancePropertiesView_RequestJavaTitle" xml:space="preserve">
        <value>Select a Java executable</value>
    </data>
    <data name="InstancePropertiesView_RequestJavaPrompt" xml:space="preserve">
        <value>Pick a file like /bin/java.exe or /bin/javaw.exe</value>
    </data>
    <data name="InstancePropertiesView_UnlockingSuccessNotificationPrompt" xml:space="preserve">
        <value>The instance is no longer associated to any modpack brand and free to edit.</value>
    </data>
    <data name="InstancePropertiesView_RequestThumbnailTitle" xml:space="preserve">
        <value>Select thumbnail</value>
    </data>
    <data name="InstancePropertiesView_RequestThumbnailPrompt" xml:space="preserve">
        <value>Select a image file</value>
    </data>
    <data name="InstancePropertiesView_ThumbnailSettingDangerNotificationTitle" xml:space="preserve">
        <value>Set instance thumbnail</value>
    </data>
    <data name="InstancePropertiesView_ThumbnailSettingDangerNotificationPrompt" xml:space="preserve">
        <value>Selected file is not a valid image or no file selected.</value>
    </data>
    <data name="InstancePropertiesView_RequestNameTitle" xml:space="preserve">
        <value>Rename instance</value>
    </data>
    <data name="InstancePropertiesView_RequestNamePrompt" xml:space="preserve">
        <value>Give the instance a new name</value>
    </data>
    <data name="PackageBulkUpdaterModal_UpdateButtonText" xml:space="preserve">
        <value>Update Checked</value>
    </data>
    <data name="PackageBulkUpdaterModal_Title" xml:space="preserve">
        <value>Update packages in bulk</value>
    </data>
    <data name="PackageContainer_DependencyGraphMenuText" xml:space="preserve">
        <value>Dependency Graph</value>
    </data>
    <data name="Enum_On" xml:space="preserve">
        <value>On</value>
    </data>
    <data name="Enum_Off" xml:space="preserve">
        <value>Off</value>
    </data>
    <data name="InstancePropertiesView_FastLaunchOnText" xml:space="preserve">
        <value>I'm in a hurry</value>
    </data>
    <data name="InstancePropertiesView_ResolvePackageDependenciesOnText" xml:space="preserve">
        <value>I know what I'm doing</value>
    </data>
    <data name="Enum_Vanilla" xml:space="preserve">
        <value>Vanilla</value>
    </data>
    <data name="Enum_Unknown" xml:space="preserve">
        <value>Unknown</value>
    </data>
    <data name="MainWindow_InstanceInstallingDangerNotificationTitle" xml:space="preserve">
        <value>Failed to install {0}</value>
    </data>
    <data name="MainWindow_InstanceInstallingSuccessNotificationPrompt" xml:space="preserve">
        <value>The instance has been installed</value>
    </data>
    <data name="MainWindow_InstanceInstallingSuccessNotificationOpenText" xml:space="preserve">
        <value>Open</value>
    </data>
    <data name="MainWindow_InstanceUpdatingDangerNotificationTitle" xml:space="preserve">
        <value>Failed to update {0}</value>
    </data>
    <data name="MainWindow_InstanceUpdatingSuccessNotificationPrompt" xml:space="preserve">
        <value>The instance has been updated</value>
    </data>
    <data name="MainWindow_InstanceUpdatingSuccessNotificationOpenText" xml:space="preserve">
        <value>Open</value>
    </data>
    <data name="MainWindow_InstanceDeployingNotificationTitle" xml:space="preserve">
        <value>Failed to deploy {0}</value>
    </data>
    <data name="MainWindow_InstanceDeployingSuccessNotificationPrompt" xml:space="preserve">
        <value>The instance has been deployed</value>
    </data>
    <data name="MainWindow_InstanceLaunchingSuccessNotificationPrompt" xml:space="preserve">
        <value>The instance has been exited</value>
    </data>
    <data name="MainWindow_InstanceLaunchingDangerNotificationViewOutputText" xml:space="preserve">
        <value>View Output</value>
    </data>
    <data name="LaunchMode_Managed" xml:space="preserve">
        <value>Managed</value>
    </data>
    <data name="LaunchMode_Debug" xml:space="preserve">
        <value>Debug</value>
    </data>
    <data name="LaunchMode_FireAndForget" xml:space="preserve">
        <value>Fire &amp; Forget</value>
    </data>
    <data name="SettingsView_TitleBarVisibilityLabelText" xml:space="preserve">
        <value>Title Bar Visibility</value>
    </data>
    <data name="DeployStage_CheckArtifact" xml:space="preserve">
        <value>Checking artifacts...</value>
    </data>
    <data name="DeployStage_InstallVanilla" xml:space="preserve">
        <value>Installing vanilla...</value>
    </data>
    <data name="DeployStage_ProcessLoader" xml:space="preserve">
        <value>Processing loader...</value>
    </data>
    <data name="DeployStage_ResolvePackage" xml:space="preserve">
        <value>Resolving packages...</value>
    </data>
    <data name="DeployStage_BuildArtifact" xml:space="preserve">
        <value>Building artifacts...</value>
    </data>
    <data name="DeployStage_GenerateManifest" xml:space="preserve">
        <value>Generating manifest...</value>
    </data>
    <data name="DeployStage_SolidifyManifest" xml:space="preserve">
        <value>Solidifying files...</value>
    </data>
    <data name="InstanceSetupView_PackageBulkUpdatingProgressingNotificationTitle" xml:space="preserve">
        <value>Update packages in bulk</value>
    </data>
    <data name="InstanceSetupView_PackageBulkUpdatingProgressingNotificationPrompt" xml:space="preserve">
        <value>Checking updates...({0}/{1})</value>
    </data>
    <data name="InstanceSetupView_PackageBulkUpdatingProgressingNotificationCancelText" xml:space="preserve">
        <value>Cancel</value>
    </data>
    <data name="InstanceSetupView_PackageBulkUpdatingProgressedNotificationTitle" xml:space="preserve">
        <value>Update packages in bulk</value>
    </data>
    <data name="InstanceSetupView_PackageBulkUpdatingProgressedNotificationPrompt" xml:space="preserve">
        <value>{0} packages to be updated</value>
    </data>
    <data name="InstanceSetupView_PackageBulkUpdatingProgressedNotificationReviewText" xml:space="preserve">
        <value>Review</value>
    </data>
    <data name="MarketplaceSearchView_ModpackInstallingNotificationPrompt" xml:space="preserve">
        <value>{0} has been added to the queue</value>
    </data>
    <data name="MarketplaceSearchView_ModpackLoadingDangerNotificationTitle" xml:space="preserve">
        <value>Loading project information failed</value>
    </data>
    <data name="ResourceKind_World" xml:space="preserve">
        <value>World</value>
    </data>
    <data name="PackageContainer_StatusLabelText" xml:space="preserve">
        <value>Status</value>
    </data>
    <data name="InstancePropertiesView_ThumbnailLabelText" xml:space="preserve">
        <value>Thumbnail</value>
    </data>
    <data name="InstancePropertiesView_DebugTitle" xml:space="preserve">
        <value>Debug</value>
    </data>
    <data name="InstancePropertiesView_CheckIntegrityButtonText" xml:space="preserve">
        <value>Build in Full Check Mode</value>
    </data>
    <data name="InstancePropertiesView_DebugSubtitle" xml:space="preserve">
        <value>Having problems? Try these options</value>
    </data>
    <data name="InstancePropertiesView_CheckIntegrityLabelText" xml:space="preserve">
        <value>Check Integrity</value>
    </data>
    <data name="InstancePackageDependencyButton_RequiredTagText" xml:space="preserve">
        <value>Required</value>
    </data>
    <data name="InstancePackageDependencyButton_RefCountTagText" xml:space="preserve">
        <value>Refs:</value>
    </data>
    <data name="InstancePackageModal_DependenciesTabText" xml:space="preserve">
        <value>Dependencies</value>
    </data>
    <data name="InstancePackageModal_BasicsTabText" xml:space="preserve">
        <value>Basics</value>
    </data>
    <data name="SettingsView_AccentColorLabelText" xml:space="preserve">
        <value>Accent Color</value>
    </data>
    <data name="DeployStage_EnsureRuntime" xml:space="preserve">
        <value>Ensuring runtime...</value>
    </data>
    <data name="MainWindow_PlayMenuText" xml:space="preserve">
        <value>Play</value>
    </data>
    <data name="MainWindow_DeployMenuText" xml:space="preserve">
        <value>Deploy</value>
    </data>
    <data name="MainWindow_OpenFolderMenuText" xml:space="preserve">
        <value>Open Folder</value>
    </data>
    <data name="MainWindow_SetupMenuText" xml:space="preserve">
        <value>Setup</value>
    </data>
    <data name="MainWindow_PropertiesMenuText" xml:space="preserve">
        <value>Properties</value>
    </data>
    <data name="Enum_Day" xml:space="preserve">
        <value>Day</value>
    </data>
    <data name="Enum_Week" xml:space="preserve">
        <value>Week</value>
    </data>
    <data name="Enum_Month" xml:space="preserve">
        <value>Month</value>
    </data>
    <data name="Enum_Year" xml:space="preserve">
        <value>Year</value>
    </data>
    <data name="Week_Sunday" xml:space="preserve">
        <value>Sun</value>
    </data>
    <data name="Week_Monday" xml:space="preserve">
        <value>Mon</value>
    </data>
    <data name="Week_Tuesday" xml:space="preserve">
        <value>Tue</value>
    </data>
    <data name="Week_Wednesday" xml:space="preserve">
        <value>Wed</value>
    </data>
    <data name="Week_Thursday" xml:space="preserve">
        <value>Thu</value>
    </data>
    <data name="Week_Friday" xml:space="preserve">
        <value>Fri</value>
    </data>
    <data name="Week_Saturday" xml:space="preserve">
        <value>Sat</value>
    </data>
    <data name="Enum_Hour" xml:space="preserve">
        <value>Hour</value>
    </data>
    <data name="SettingsView_CornerStyleLabelText" xml:space="preserve">
        <value>Corner Radius</value>
    </data>
</root>