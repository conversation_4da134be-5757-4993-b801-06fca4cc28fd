//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Polymerium.App.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "4.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Polymerium.App.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The account has been linked. Checkout in the next page..
        /// </summary>
        public static string AccountCreationMicrosoft_DoneSubtitle {
            get {
                return ResourceManager.GetString("AccountCreationMicrosoft_DoneSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Done!.
        /// </summary>
        public static string AccountCreationMicrosoft_DoneTitle {
            get {
                return ResourceManager.GetString("AccountCreationMicrosoft_DoneTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open in the browser.
        /// </summary>
        public static string AccountCreationMicrosoft_OpenLinkButtonText {
            get {
                return ResourceManager.GetString("AccountCreationMicrosoft_OpenLinkButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The code will be expired in 15 minutes. Please fill this code in the page:.
        /// </summary>
        public static string AccountCreationMicrosoft_Prompt {
            get {
                return ResourceManager.GetString("AccountCreationMicrosoft_Prompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Retry.
        /// </summary>
        public static string AccountCreationMicrosoft_RetryButtonText {
            get {
                return ResourceManager.GetString("AccountCreationMicrosoft_RetryButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Microsoft Authentication.
        /// </summary>
        public static string AccountCreationMicrosoft_Title {
            get {
                return ResourceManager.GetString("AccountCreationMicrosoft_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Something went wrong....
        /// </summary>
        public static string AccountCreationMicrosoft_UnavailableLabelText {
            get {
                return ResourceManager.GetString("AccountCreationMicrosoft_UnavailableLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Back.
        /// </summary>
        public static string AccountCreationModal_BackButtonText {
            get {
                return ResourceManager.GetString("AccountCreationModal_BackButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dismiss.
        /// </summary>
        public static string AccountCreationModal_DismissButtonText {
            get {
                return ResourceManager.GetString("AccountCreationModal_DismissButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Finish.
        /// </summary>
        public static string AccountCreationModal_FinishButtonText {
            get {
                return ResourceManager.GetString("AccountCreationModal_FinishButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Next.
        /// </summary>
        public static string AccountCreationModal_NextButtonText {
            get {
                return ResourceManager.GetString("AccountCreationModal_NextButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string AccountCreationOffline_NameLabelText {
            get {
                return ResourceManager.GetString("AccountCreationOffline_NameLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name containing spaces or other non-ascii characters may cause issues..
        /// </summary>
        public static string AccountCreationOffline_Prompt {
            get {
                return ResourceManager.GetString("AccountCreationOffline_Prompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pick a name.
        /// </summary>
        public static string AccountCreationOffline_Title {
            get {
                return ResourceManager.GetString("AccountCreationOffline_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UUID.
        /// </summary>
        public static string AccountCreationOffline_UuidLabelText {
            get {
                return ResourceManager.GetString("AccountCreationOffline_UuidLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enables all the online features and is the only way to play the game legally..
        /// </summary>
        public static string AccountCreationPortal_MicrosoftSubtitle {
            get {
                return ResourceManager.GetString("AccountCreationPortal_MicrosoftSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Microsoft.
        /// </summary>
        public static string AccountCreationPortal_MicrosoftTitle {
            get {
                return ResourceManager.GetString("AccountCreationPortal_MicrosoftTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Nothing good but no Internet connection required..
        /// </summary>
        public static string AccountCreationPortal_OfflineSubtitle {
            get {
                return ResourceManager.GetString("AccountCreationPortal_OfflineSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offline.
        /// </summary>
        public static string AccountCreationPortal_OfflineTitle {
            get {
                return ResourceManager.GetString("AccountCreationPortal_OfflineTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Offline account is unavailable if no Microsoft account is linked..
        /// </summary>
        public static string AccountCreationPortal_Prompt {
            get {
                return ResourceManager.GetString("AccountCreationPortal_Prompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pick an account type.
        /// </summary>
        public static string AccountCreationPortal_Title {
            get {
                return ResourceManager.GetString("AccountCreationPortal_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Play as the preset character if dont have an account..
        /// </summary>
        public static string AccountCreationPortal_TrialSubtitle {
            get {
                return ResourceManager.GetString("AccountCreationPortal_TrialSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Trial.
        /// </summary>
        public static string AccountCreationPortal_TrialTitle {
            get {
                return ResourceManager.GetString("AccountCreationPortal_TrialTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 🎉It&apos;s nearly done🎉.
        /// </summary>
        public static string AccountCreationPreview_Subtitle {
            get {
                return ResourceManager.GetString("AccountCreationPreview_Subtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account Preview.
        /// </summary>
        public static string AccountCreationPreview_Title {
            get {
                return ResourceManager.GetString("AccountCreationPreview_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pick your favorite family guy.
        /// </summary>
        public static string AccountCreationTrial_Title {
            get {
                return ResourceManager.GetString("AccountCreationTrial_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enrolled.
        /// </summary>
        public static string AccountEntryModal_EnrolledLabelText {
            get {
                return ResourceManager.GetString("AccountEntryModal_EnrolledLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Last Used.
        /// </summary>
        public static string AccountEntryModal_LastUsedLabelText {
            get {
                return ResourceManager.GetString("AccountEntryModal_LastUsedLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This kind of account is kind of: No use in the past then no use in the future any more..
        /// </summary>
        public static string AccountEntryModal_MicrosoftPrompt {
            get {
                return ResourceManager.GetString("AccountEntryModal_MicrosoftPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to It&apos;s all set..
        /// </summary>
        public static string AccountEntryModal_OfflinePrompt {
            get {
                return ResourceManager.GetString("AccountEntryModal_OfflinePrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to It&apos;s all set..
        /// </summary>
        public static string AccountEntryModal_TrialPrompt {
            get {
                return ResourceManager.GetString("AccountEntryModal_TrialPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No account present in the vault.
        /// </summary>
        public static string AccountPickerDialog_EmptyListPrompt {
            get {
                return ResourceManager.GetString("AccountPickerDialog_EmptyListPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Manage Accounts.
        /// </summary>
        public static string AccountPickerDialog_ManageAccountsButtonText {
            get {
                return ResourceManager.GetString("AccountPickerDialog_ManageAccountsButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose a game account.
        /// </summary>
        public static string AccountPickerDialog_Title {
            get {
                return ResourceManager.GetString("AccountPickerDialog_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account with the same uuid already exists.
        /// </summary>
        public static string AccountsView_AccountAddingDangerNotificationPrompt {
            get {
                return ResourceManager.GetString("AccountsView_AccountAddingDangerNotificationPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account creation failed.
        /// </summary>
        public static string AccountsView_AccountAddingDangerNotificationTitle {
            get {
                return ResourceManager.GetString("AccountsView_AccountAddingDangerNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add Account.
        /// </summary>
        public static string AccountsView_AddAccountButtonText {
            get {
                return ResourceManager.GetString("AccountsView_AddAccountButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mark as Default.
        /// </summary>
        public static string AccountsView_MarkAsDefaultMenuText {
            get {
                return ResourceManager.GetString("AccountsView_MarkAsDefaultMenuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove.
        /// </summary>
        public static string AccountsView_RemoveMenuText {
            get {
                return ResourceManager.GetString("AccountsView_RemoveMenuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accounts.
        /// </summary>
        public static string AccountsView_Title {
            get {
                return ResourceManager.GetString("AccountsView_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Building artifacts....
        /// </summary>
        public static string DeployStage_BuildArtifact {
            get {
                return ResourceManager.GetString("DeployStage_BuildArtifact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Checking artifacts....
        /// </summary>
        public static string DeployStage_CheckArtifact {
            get {
                return ResourceManager.GetString("DeployStage_CheckArtifact", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Ensuring runtime....
        /// </summary>
        public static string DeployStage_EnsureRuntime {
            get {
                return ResourceManager.GetString("DeployStage_EnsureRuntime", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Generating manifest....
        /// </summary>
        public static string DeployStage_GenerateManifest {
            get {
                return ResourceManager.GetString("DeployStage_GenerateManifest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Installing vanilla....
        /// </summary>
        public static string DeployStage_InstallVanilla {
            get {
                return ResourceManager.GetString("DeployStage_InstallVanilla", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Processing loader....
        /// </summary>
        public static string DeployStage_ProcessLoader {
            get {
                return ResourceManager.GetString("DeployStage_ProcessLoader", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resolving packages....
        /// </summary>
        public static string DeployStage_ResolvePackage {
            get {
                return ResourceManager.GetString("DeployStage_ResolvePackage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Solidifying files....
        /// </summary>
        public static string DeployStage_SolidifyManifest {
            get {
                return ResourceManager.GetString("DeployStage_SolidifyManifest", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string Dialog_CancelButtonText {
            get {
                return ResourceManager.GetString("Dialog_CancelButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Confirm.
        /// </summary>
        public static string Dialog_ConfirmButtonText {
            get {
                return ResourceManager.GetString("Dialog_ConfirmButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dismiss.
        /// </summary>
        public static string Dialog_DismissButtonText {
            get {
                return ResourceManager.GetString("Dialog_DismissButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All.
        /// </summary>
        public static string Enum_All {
            get {
                return ResourceManager.GetString("Enum_All", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Day.
        /// </summary>
        public static string Enum_Day {
            get {
                return ResourceManager.GetString("Enum_Day", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disabled.
        /// </summary>
        public static string Enum_Disabled {
            get {
                return ResourceManager.GetString("Enum_Disabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enabled.
        /// </summary>
        public static string Enum_Enabled {
            get {
                return ResourceManager.GetString("Enum_Enabled", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Hour.
        /// </summary>
        public static string Enum_Hour {
            get {
                return ResourceManager.GetString("Enum_Hour", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Month.
        /// </summary>
        public static string Enum_Month {
            get {
                return ResourceManager.GetString("Enum_Month", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None.
        /// </summary>
        public static string Enum_None {
            get {
                return ResourceManager.GetString("Enum_None", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Off.
        /// </summary>
        public static string Enum_Off {
            get {
                return ResourceManager.GetString("Enum_Off", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to On.
        /// </summary>
        public static string Enum_On {
            get {
                return ResourceManager.GetString("Enum_On", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unknown.
        /// </summary>
        public static string Enum_Unknown {
            get {
                return ResourceManager.GetString("Enum_Unknown", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Vanilla.
        /// </summary>
        public static string Enum_Vanilla {
            get {
                return ResourceManager.GetString("Enum_Vanilla", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Week.
        /// </summary>
        public static string Enum_Week {
            get {
                return ResourceManager.GetString("Enum_Week", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Year.
        /// </summary>
        public static string Enum_Year {
            get {
                return ResourceManager.GetString("Enum_Year", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to It seems something went wrong....
        /// </summary>
        public static string ExceptionView_Subtitle {
            get {
                return ResourceManager.GetString("ExceptionView_Subtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Died, but not dead....
        /// </summary>
        public static string ExceptionView_Title {
            get {
                return ResourceManager.GetString("ExceptionView_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required.
        /// </summary>
        public static string ExhibitDependencyButton_RequiredTagText {
            get {
                return ResourceManager.GetString("ExhibitDependencyButton_RequiredTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Install.
        /// </summary>
        public static string ExhibitModpackButton_InstallButtonText {
            get {
                return ResourceManager.GetString("ExhibitModpackButton_InstallButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Install.
        /// </summary>
        public static string ExhibitModpackToast_InstallButtonText {
            get {
                return ResourceManager.GetString("ExhibitModpackToast_InstallButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to About.
        /// </summary>
        public static string ExhibitPackageModal_AboutTabText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_AboutTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add to the instance.
        /// </summary>
        public static string ExhibitPackageModal_AddButtonText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_AddButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adding:.
        /// </summary>
        public static string ExhibitPackageModal_AddingVersionTagText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_AddingVersionTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Showing changelog of version:.
        /// </summary>
        public static string ExhibitPackageModal_ChangelogsTabPrompt {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_ChangelogsTabPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Changlogs.
        /// </summary>
        public static string ExhibitPackageModal_ChangelogsTabText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_ChangelogsTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Showing dependencies of version:.
        /// </summary>
        public static string ExhibitPackageModal_DependenciesTabPrompt {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_DependenciesTabPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dependencies.
        /// </summary>
        public static string ExhibitPackageModal_DependenciesTabText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_DependenciesTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a Version.
        /// </summary>
        public static string ExhibitPackageModal_EmptyListLabelText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_EmptyListLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show compatible versions only.
        /// </summary>
        public static string ExhibitPackageModal_FilterLabelText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_FilterLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Installed:.
        /// </summary>
        public static string ExhibitPackageModal_InstalledVersionTagText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_InstalledVersionTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Locked Version.
        /// </summary>
        public static string ExhibitPackageModal_LockedVersionLabelText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_LockedVersionLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Apply changes.
        /// </summary>
        public static string ExhibitPackageModal_ModifyButtonText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_ModifyButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Replacing:.
        /// </summary>
        public static string ExhibitPackageModal_ModifyingTagText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_ModifyingTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Removing:.
        /// </summary>
        public static string ExhibitPackageModal_RemovingTagText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_RemovingTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Restore.
        /// </summary>
        public static string ExhibitPackageModal_RestoreButtonText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_RestoreButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unspecified.
        /// </summary>
        public static string ExhibitPackageModal_UnspecifiedVersionTagText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_UnspecifiedVersionTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Specify version....
        /// </summary>
        public static string ExhibitPackageModal_VersionBoxPlaceholder {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_VersionBoxPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version:.
        /// </summary>
        public static string ExhibitPackageModal_VersionLabelText {
            get {
                return ResourceManager.GetString("ExhibitPackageModal_VersionLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Adding.
        /// </summary>
        public static string ExhibitStatePresenter_AddingTagText {
            get {
                return ResourceManager.GetString("ExhibitStatePresenter_AddingTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Installed.
        /// </summary>
        public static string ExhibitStatePresenter_EditableTagText {
            get {
                return ResourceManager.GetString("ExhibitStatePresenter_EditableTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Locked.
        /// </summary>
        public static string ExhibitStatePresenter_LockedTagText {
            get {
                return ResourceManager.GetString("ExhibitStatePresenter_LockedTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Modifying.
        /// </summary>
        public static string ExhibitStatePresenter_ModifyingTagText {
            get {
                return ResourceManager.GetString("ExhibitStatePresenter_ModifyingTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Removing.
        /// </summary>
        public static string ExhibitStatePresenter_RemovingTagText {
            get {
                return ResourceManager.GetString("ExhibitStatePresenter_RemovingTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to packages.
        /// </summary>
        public static string ExportPackageListDialog_PackageCountLabelText {
            get {
                return ResourceManager.GetString("ExportPackageListDialog_PackageCountLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chooooosen file path.
        /// </summary>
        public static string ExportPackageListDialog_PathBarPlaceholder {
            get {
                return ResourceManager.GetString("ExportPackageListDialog_PathBarPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export to.
        /// </summary>
        public static string ExportPackageListDialog_PathLabelText {
            get {
                return ResourceManager.GetString("ExportPackageListDialog_PathLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export package list of the instance into the selected formatted table file.
        /// </summary>
        public static string ExportPackageListDialog_Prompt {
            get {
                return ResourceManager.GetString("ExportPackageListDialog_Prompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Export package list.
        /// </summary>
        public static string ExportPackageListDialog_Title {
            get {
                return ResourceManager.GetString("ExportPackageListDialog_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to File too big makes app crash.
        /// </summary>
        public static string FilePickerDialog_AlertPrompt {
            get {
                return ResourceManager.GetString("FilePickerDialog_AlertPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browser files....
        /// </summary>
        public static string FilePickerDialog_BrowseButtonText {
            get {
                return ResourceManager.GetString("FilePickerDialog_BrowseButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Drag and drop, or.
        /// </summary>
        public static string FilePickerDialog_DropZonePrompt {
            get {
                return ResourceManager.GetString("FilePickerDialog_DropZonePrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Chooooosen file path&quot;.
        /// </summary>
        public static string FilePickerDialog_PathBarPlaceholder {
            get {
                return ResourceManager.GetString("FilePickerDialog_PathBarPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pick a file.
        /// </summary>
        public static string FilePickerDialog_Title {
            get {
                return ResourceManager.GetString("FilePickerDialog_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pick a game version.
        /// </summary>
        public static string GameVersionPickerDialog_Title {
            get {
                return ResourceManager.GetString("GameVersionPickerDialog_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter by version names....
        /// </summary>
        public static string GameVersionPickerDialog_VersionBarPlaceholder {
            get {
                return ResourceManager.GetString("GameVersionPickerDialog_VersionBarPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to INSTALLING.
        /// </summary>
        public static string InstanceEntryButton_InstallTagText {
            get {
                return ResourceManager.GetString("InstanceEntryButton_InstallTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to PREPARING.
        /// </summary>
        public static string InstanceEntryButton_PreparingTagText {
            get {
                return ResourceManager.GetString("InstanceEntryButton_PreparingTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to RUNNING.
        /// </summary>
        public static string InstanceEntryButton_RunningTagText {
            get {
                return ResourceManager.GetString("InstanceEntryButton_RunningTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to UPDATING.
        /// </summary>
        public static string InstanceEntryButton_UpdatingTagText {
            get {
                return ResourceManager.GetString("InstanceEntryButton_UpdatingTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ABORT.
        /// </summary>
        public static string InstanceHomeView_AbortButtonText {
            get {
                return ResourceManager.GetString("InstanceHomeView_AbortButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account authentication failed.
        /// </summary>
        public static string InstanceHomeView_AccountAuthenticationDangerNotificationTitle {
            get {
                return ResourceManager.GetString("InstanceHomeView_AccountAuthenticationDangerNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unchosen.
        /// </summary>
        public static string InstanceHomeView_AccountButtonSubtitle {
            get {
                return ResourceManager.GetString("InstanceHomeView_AccountButtonSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account.
        /// </summary>
        public static string InstanceHomeView_AccountButtonTitle {
            get {
                return ResourceManager.GetString("InstanceHomeView_AccountButtonTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account is not provided or removed after set.
        /// </summary>
        public static string InstanceHomeView_AccountNotFoundDangerNotificationPrompt {
            get {
                return ResourceManager.GetString("InstanceHomeView_AccountNotFoundDangerNotificationPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select Account.
        /// </summary>
        public static string InstanceHomeView_AccountNotFoundDangerNotificationSelectActionText {
            get {
                return ResourceManager.GetString("InstanceHomeView_AccountNotFoundDangerNotificationSelectActionText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account Not Found.
        /// </summary>
        public static string InstanceHomeView_AccountNotFoundDangerNotificationTitle {
            get {
                return ResourceManager.GetString("InstanceHomeView_AccountNotFoundDangerNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activities.
        /// </summary>
        public static string InstanceHomeView_ActivitiesTitle {
            get {
                return ResourceManager.GetString("InstanceHomeView_ActivitiesTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dashboard.
        /// </summary>
        public static string InstanceHomeView_DashboardButtonSubtitle {
            get {
                return ResourceManager.GetString("InstanceHomeView_DashboardButtonSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Logs.
        /// </summary>
        public static string InstanceHomeView_DashboardButtonTitle {
            get {
                return ResourceManager.GetString("InstanceHomeView_DashboardButtonTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deployment failed.
        /// </summary>
        public static string InstanceHomeView_DeployDangerNotificationTitle {
            get {
                return ResourceManager.GetString("InstanceHomeView_DeployDangerNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Eject.
        /// </summary>
        public static string InstanceHomeView_DetachButtonText {
            get {
                return ResourceManager.GetString("InstanceHomeView_DetachButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Total Hours.
        /// </summary>
        public static string InstanceHomeView_HourCountText {
            get {
                return ResourceManager.GetString("InstanceHomeView_HourCountText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to KILL.
        /// </summary>
        public static string InstanceHomeView_KillButtonText {
            get {
                return ResourceManager.GetString("InstanceHomeView_KillButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to LAUNCH.
        /// </summary>
        public static string InstanceHomeView_LaunchButtonText {
            get {
                return ResourceManager.GetString("InstanceHomeView_LaunchButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Launch Pad.
        /// </summary>
        public static string InstanceHomeView_LaunchPadTitle {
            get {
                return ResourceManager.GetString("InstanceHomeView_LaunchPadTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Account is not set yet.
        /// </summary>
        public static string InstanceHomeView_NoAccountLabelText {
            get {
                return ResourceManager.GetString("InstanceHomeView_NoAccountLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Overview.
        /// </summary>
        public static string InstanceHomeView_OverviewTitle {
            get {
                return ResourceManager.GetString("InstanceHomeView_OverviewTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to packages.
        /// </summary>
        public static string InstanceHomeView_PackageCountText {
            get {
                return ResourceManager.GetString("InstanceHomeView_PackageCountText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Everything is ready.
        /// </summary>
        public static string InstanceHomeView_ReadyLabelText {
            get {
                return ResourceManager.GetString("InstanceHomeView_ReadyLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to See More.
        /// </summary>
        public static string InstanceHomeView_SeeMoreButtonText {
            get {
                return ResourceManager.GetString("InstanceHomeView_SeeMoreButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Details.
        /// </summary>
        public static string InstanceHomeView_SetupEditButtonText {
            get {
                return ResourceManager.GetString("InstanceHomeView_SetupEditButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup.
        /// </summary>
        public static string InstanceHomeView_SetupTitle {
            get {
                return ResourceManager.GetString("InstanceHomeView_SetupTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type.
        /// </summary>
        public static string InstanceHomeView_SetupTypeText {
            get {
                return ResourceManager.GetString("InstanceHomeView_SetupTypeText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version.
        /// </summary>
        public static string InstanceHomeView_SetupVersionText {
            get {
                return ResourceManager.GetString("InstanceHomeView_SetupVersionText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Widgets.
        /// </summary>
        public static string InstanceHomeView_WidgetsTitle {
            get {
                return ResourceManager.GetString("InstanceHomeView_WidgetsTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto Version.
        /// </summary>
        public static string InstancePackageButton_AutoVersionTagText {
            get {
                return ResourceManager.GetString("InstancePackageButton_AutoVersionTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Disabled.
        /// </summary>
        public static string InstancePackageButton_DisabledLabelText {
            get {
                return ResourceManager.GetString("InstancePackageButton_DisabledLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Original.
        /// </summary>
        public static string InstancePackageButton_OriginalTagText {
            get {
                return ResourceManager.GetString("InstancePackageButton_OriginalTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Refs:.
        /// </summary>
        public static string InstancePackageDependencyButton_RefCountTagText {
            get {
                return ResourceManager.GetString("InstancePackageDependencyButton_RefCountTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Required.
        /// </summary>
        public static string InstancePackageDependencyButton_RequiredTagText {
            get {
                return ResourceManager.GetString("InstancePackageDependencyButton_RequiredTagText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        public static string InstancePackageModal_AddTagButtonText {
            get {
                return ResourceManager.GetString("InstancePackageModal_AddTagButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Basics.
        /// </summary>
        public static string InstancePackageModal_BasicsTabText {
            get {
                return ResourceManager.GetString("InstancePackageModal_BasicsTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dependencies.
        /// </summary>
        public static string InstancePackageModal_DependenciesTabText {
            get {
                return ResourceManager.GetString("InstancePackageModal_DependenciesTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Show compatible versions only.
        /// </summary>
        public static string InstancePackageModal_FilterLabelText {
            get {
                return ResourceManager.GetString("InstancePackageModal_FilterLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Not Editable.
        /// </summary>
        public static string InstancePackageModal_LockedVersionLabelText {
            get {
                return ResourceManager.GetString("InstancePackageModal_LockedVersionLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tags.
        /// </summary>
        public static string InstancePackageModal_TagsTabText {
            get {
                return ResourceManager.GetString("InstancePackageModal_TagsTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Version.
        /// </summary>
        public static string InstancePackageModal_VersionBoxLabelText {
            get {
                return ResourceManager.GetString("InstancePackageModal_VersionBoxLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Or pick a version in the list below..
        /// </summary>
        public static string InstancePackageModal_VersionBoxUnspecificSubtitle {
            get {
                return ResourceManager.GetString("InstancePackageModal_VersionBoxUnspecificSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto Select Version.
        /// </summary>
        public static string InstancePackageModal_VersionBoxUnspecificTitle {
            get {
                return ResourceManager.GetString("InstancePackageModal_VersionBoxUnspecificTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Versions.
        /// </summary>
        public static string InstancePackageModal_VersionsTabText {
            get {
                return ResourceManager.GetString("InstancePackageModal_VersionsTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How the application acts and chooses strategies.
        /// </summary>
        public static string InstancePropertiesView_BehaviorsSubtitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_BehaviorsSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Behaviors.
        /// </summary>
        public static string InstancePropertiesView_BehaviorsTitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_BehaviorsTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Build in Full Check Mode.
        /// </summary>
        public static string InstancePropertiesView_CheckIntegrityButtonText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_CheckIntegrityButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Check Integrity.
        /// </summary>
        public static string InstancePropertiesView_CheckIntegrityLabelText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_CheckIntegrityLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to These settings are applied instantly and can&apos;t be recovered.
        /// </summary>
        public static string InstancePropertiesView_DangerZoneSubtitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_DangerZoneSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Danger Zone.
        /// </summary>
        public static string InstancePropertiesView_DangerZoneTitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_DangerZoneTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Having problems? Try these options.
        /// </summary>
        public static string InstancePropertiesView_DebugSubtitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_DebugSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debug.
        /// </summary>
        public static string InstancePropertiesView_DebugTitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_DebugTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Delete.
        /// </summary>
        public static string InstancePropertiesView_DeleteButtonText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_DeleteButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Display Name.
        /// </summary>
        public static string InstancePropertiesView_DisplayNameLabelText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_DisplayNameLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fast Launch.
        /// </summary>
        public static string InstancePropertiesView_FastLaunchLabelText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_FastLaunchLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I&apos;m in a hurry.
        /// </summary>
        public static string InstancePropertiesView_FastLaunchOnText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_FastLaunchOnText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Skip the asset and file validation process if the instance is already built and launch the game directly. May lead to crash if the assets are corrupted..
        /// </summary>
        public static string InstancePropertiesView_FastLaunchPrompt {
            get {
                return ResourceManager.GetString("InstancePropertiesView_FastLaunchPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The instance is busy now.
        /// </summary>
        public static string InstancePropertiesView_InoperableLabelText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_InoperableLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Java Additional Arguments.
        /// </summary>
        public static string InstancePropertiesView_JavaAdditionalArgumentsLabelText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_JavaAdditionalArgumentsLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Java Home.
        /// </summary>
        public static string InstancePropertiesView_JavaLabelText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_JavaLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Java Max Memory.
        /// </summary>
        public static string InstancePropertiesView_JavaMaxMemoryLabelText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_JavaMaxMemoryLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MiB.
        /// </summary>
        public static string InstancePropertiesView_JavaMaxMemoryUnitText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_JavaMaxMemoryUnitText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Link.
        /// </summary>
        public static string InstancePropertiesView_LinkerLabelText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_LinkerLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unlock by removing the attached pack brand/metadata that will make the instance losing the ability of updating but gaining the ability of editing all the imported packages..
        /// </summary>
        public static string InstancePropertiesView_LinkerPrompt {
            get {
                return ResourceManager.GetString("InstancePropertiesView_LinkerPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Entries here will overwrite the global settings.
        /// </summary>
        public static string InstancePropertiesView_NavigateToGlobalButtonSubtitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_NavigateToGlobalButtonSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Navigate to the global settings page.
        /// </summary>
        public static string InstancePropertiesView_NavigateToGlobalButtonTitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_NavigateToGlobalButtonTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Configuration specified to the instance; left blank to refer to the global settings.
        /// </summary>
        public static string InstancePropertiesView_OverridesSubtitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_OverridesSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Game Overrides.
        /// </summary>
        public static string InstancePropertiesView_OverridesTitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_OverridesTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove.
        /// </summary>
        public static string InstancePropertiesView_RemoveButtonText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_RemoveButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rename.
        /// </summary>
        public static string InstancePropertiesView_RenameButtonText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_RenameButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pick a file like /bin/java.exe or /bin/javaw.exe.
        /// </summary>
        public static string InstancePropertiesView_RequestJavaPrompt {
            get {
                return ResourceManager.GetString("InstancePropertiesView_RequestJavaPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a Java executable.
        /// </summary>
        public static string InstancePropertiesView_RequestJavaTitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_RequestJavaTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Give the instance a new name.
        /// </summary>
        public static string InstancePropertiesView_RequestNamePrompt {
            get {
                return ResourceManager.GetString("InstancePropertiesView_RequestNamePrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Rename instance.
        /// </summary>
        public static string InstancePropertiesView_RequestNameTitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_RequestNameTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a image file.
        /// </summary>
        public static string InstancePropertiesView_RequestThumbnailPrompt {
            get {
                return ResourceManager.GetString("InstancePropertiesView_RequestThumbnailPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select thumbnail.
        /// </summary>
        public static string InstancePropertiesView_RequestThumbnailTitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_RequestThumbnailTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Reset.
        /// </summary>
        public static string InstancePropertiesView_ResetButtonText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_ResetButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to [Experimental] Resolve Package Dependencies.
        /// </summary>
        public static string InstancePropertiesView_ResolvePackageDependenciesLabelText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_ResolvePackageDependenciesLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to I know what I&apos;m doing.
        /// </summary>
        public static string InstancePropertiesView_ResolvePackageDependenciesOnText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_ResolvePackageDependenciesOnText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to This always leads to unexpected package conflicts due to poor management of package distribution sites. .
        /// </summary>
        public static string InstancePropertiesView_ResolvePackageDependenciesPrompt {
            get {
                return ResourceManager.GetString("InstancePropertiesView_ResolvePackageDependenciesPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Scrifice.
        /// </summary>
        public static string InstancePropertiesView_SacrificeLabelText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_SacrificeLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select.
        /// </summary>
        public static string InstancePropertiesView_SelectButtonText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_SelectButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thumbnail.
        /// </summary>
        public static string InstancePropertiesView_ThumbnailLabelText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_ThumbnailLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thumbnail saving failed.
        /// </summary>
        public static string InstancePropertiesView_ThumbnailSavingDangerNotificationTitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_ThumbnailSavingDangerNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected file is not a valid image or no file selected..
        /// </summary>
        public static string InstancePropertiesView_ThumbnailSettingDangerNotificationPrompt {
            get {
                return ResourceManager.GetString("InstancePropertiesView_ThumbnailSettingDangerNotificationPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Set instance thumbnail.
        /// </summary>
        public static string InstancePropertiesView_ThumbnailSettingDangerNotificationTitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_ThumbnailSettingDangerNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to How this instance identify itself.
        /// </summary>
        public static string InstancePropertiesView_TitleSubtitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_TitleSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Title.
        /// </summary>
        public static string InstancePropertiesView_TitleTitle {
            get {
                return ResourceManager.GetString("InstancePropertiesView_TitleTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unlock.
        /// </summary>
        public static string InstancePropertiesView_UnlockButtonText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_UnlockButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The instance is no longer associated to any modpack brand and free to edit..
        /// </summary>
        public static string InstancePropertiesView_UnlockingSuccessNotificationPrompt {
            get {
                return ResourceManager.GetString("InstancePropertiesView_UnlockingSuccessNotificationPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Height.
        /// </summary>
        public static string InstancePropertiesView_WindowHeightLabelText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_WindowHeightLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Window Initial Size.
        /// </summary>
        public static string InstancePropertiesView_WindowInitialSizeLabelText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_WindowInitialSizeLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Width.
        /// </summary>
        public static string InstancePropertiesView_WindowWidthLabelText {
            get {
                return ResourceManager.GetString("InstancePropertiesView_WindowWidthLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Operating In Progress.
        /// </summary>
        public static string InstanceSetupView_InoperableLabelText {
            get {
                return ResourceManager.GetString("InstanceSetupView_InoperableLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mod Loader.
        /// </summary>
        public static string InstanceSetupView_LoaderLabelText {
            get {
                return ResourceManager.GetString("InstanceSetupView_LoaderLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading packages information....
        /// </summary>
        public static string InstanceSetupView_LoadingPackageLabelText {
            get {
                return ResourceManager.GetString("InstanceSetupView_LoadingPackageLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} packages to be updated.
        /// </summary>
        public static string InstanceSetupView_PackageBulkUpdatingProgressedNotificationPrompt {
            get {
                return ResourceManager.GetString("InstanceSetupView_PackageBulkUpdatingProgressedNotificationPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Review.
        /// </summary>
        public static string InstanceSetupView_PackageBulkUpdatingProgressedNotificationReviewText {
            get {
                return ResourceManager.GetString("InstanceSetupView_PackageBulkUpdatingProgressedNotificationReviewText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update packages in bulk.
        /// </summary>
        public static string InstanceSetupView_PackageBulkUpdatingProgressedNotificationTitle {
            get {
                return ResourceManager.GetString("InstanceSetupView_PackageBulkUpdatingProgressedNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Cancel.
        /// </summary>
        public static string InstanceSetupView_PackageBulkUpdatingProgressingNotificationCancelText {
            get {
                return ResourceManager.GetString("InstanceSetupView_PackageBulkUpdatingProgressingNotificationCancelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Checking updates...({0}/{1}).
        /// </summary>
        public static string InstanceSetupView_PackageBulkUpdatingProgressingNotificationPrompt {
            get {
                return ResourceManager.GetString("InstanceSetupView_PackageBulkUpdatingProgressingNotificationPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update packages in bulk.
        /// </summary>
        public static string InstanceSetupView_PackageBulkUpdatingProgressingNotificationTitle {
            get {
                return ResourceManager.GetString("InstanceSetupView_PackageBulkUpdatingProgressingNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Something went wrong.
        /// </summary>
        public static string InstanceSetupView_ReferenceUnavailableLabelText {
            get {
                return ResourceManager.GetString("InstanceSetupView_ReferenceUnavailableLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Switch Version.
        /// </summary>
        public static string InstanceSetupView_SwitchVersionButtonText {
            get {
                return ResourceManager.GetString("InstanceSetupView_SwitchVersionButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Game Version.
        /// </summary>
        public static string InstanceSetupView_VersionLabelText {
            get {
                return ResourceManager.GetString("InstanceSetupView_VersionLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Browse.
        /// </summary>
        public static string JavaHomeContainer_BrowseButtonText {
            get {
                return ResourceManager.GetString("JavaHomeContainer_BrowseButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Detect.
        /// </summary>
        public static string JavaHomeContainer_DetectButtonText {
            get {
                return ResourceManager.GetString("JavaHomeContainer_DetectButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bundled or.
        /// </summary>
        public static string JavaHomeContainer_Prompt {
            get {
                return ResourceManager.GetString("JavaHomeContainer_Prompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a Java executable.
        /// </summary>
        public static string JavaHomeContainer_ReqeustJavaTitle {
            get {
                return ResourceManager.GetString("JavaHomeContainer_ReqeustJavaTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pick a file like /bin/java.exe or /bin/javaw.exe.
        /// </summary>
        public static string JavaHomeContainer_RequestJavaPrompt {
            get {
                return ResourceManager.GetString("JavaHomeContainer_RequestJavaPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Unknown.
        /// </summary>
        public static string JavaHomeContainer_UnknownLabelText {
            get {
                return ResourceManager.GetString("JavaHomeContainer_UnknownLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Debug.
        /// </summary>
        public static string LaunchMode_Debug {
            get {
                return ResourceManager.GetString("LaunchMode_Debug", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fire &amp; Forget.
        /// </summary>
        public static string LaunchMode_FireAndForget {
            get {
                return ResourceManager.GetString("LaunchMode_FireAndForget", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Managed.
        /// </summary>
        public static string LaunchMode_Managed {
            get {
                return ResourceManager.GetString("LaunchMode_Managed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accounts.
        /// </summary>
        public static string MainWindow_AccountButtonText {
            get {
                return ResourceManager.GetString("MainWindow_AccountButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to B-b-b-bird, bird, bird, b-bird&apos;s the word....
        /// </summary>
        public static string MainWindow_BackgroundText {
            get {
                return ResourceManager.GetString("MainWindow_BackgroundText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Deploy.
        /// </summary>
        public static string MainWindow_DeployMenuText {
            get {
                return ResourceManager.GetString("MainWindow_DeployMenuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Home.
        /// </summary>
        public static string MainWindow_HomeButtonText {
            get {
                return ResourceManager.GetString("MainWindow_HomeButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to deploy {0}.
        /// </summary>
        public static string MainWindow_InstanceDeployingNotificationTitle {
            get {
                return ResourceManager.GetString("MainWindow_InstanceDeployingNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The instance has been deployed.
        /// </summary>
        public static string MainWindow_InstanceDeployingSuccessNotificationPrompt {
            get {
                return ResourceManager.GetString("MainWindow_InstanceDeployingSuccessNotificationPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter entries by names....
        /// </summary>
        public static string MainWindow_InstanceFilterPlaceholder {
            get {
                return ResourceManager.GetString("MainWindow_InstanceFilterPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to install {0}.
        /// </summary>
        public static string MainWindow_InstanceInstallingDangerNotificationTitle {
            get {
                return ResourceManager.GetString("MainWindow_InstanceInstallingDangerNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open.
        /// </summary>
        public static string MainWindow_InstanceInstallingSuccessNotificationOpenText {
            get {
                return ResourceManager.GetString("MainWindow_InstanceInstallingSuccessNotificationOpenText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The instance has been installed.
        /// </summary>
        public static string MainWindow_InstanceInstallingSuccessNotificationPrompt {
            get {
                return ResourceManager.GetString("MainWindow_InstanceInstallingSuccessNotificationPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to View Output.
        /// </summary>
        public static string MainWindow_InstanceLaunchingDangerNotificationViewOutputText {
            get {
                return ResourceManager.GetString("MainWindow_InstanceLaunchingDangerNotificationViewOutputText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The instance has been exited.
        /// </summary>
        public static string MainWindow_InstanceLaunchingSuccessNotificationPrompt {
            get {
                return ResourceManager.GetString("MainWindow_InstanceLaunchingSuccessNotificationPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to update {0}.
        /// </summary>
        public static string MainWindow_InstanceUpdatingDangerNotificationTitle {
            get {
                return ResourceManager.GetString("MainWindow_InstanceUpdatingDangerNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open.
        /// </summary>
        public static string MainWindow_InstanceUpdatingSuccessNotificationOpenText {
            get {
                return ResourceManager.GetString("MainWindow_InstanceUpdatingSuccessNotificationOpenText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The instance has been updated.
        /// </summary>
        public static string MainWindow_InstanceUpdatingSuccessNotificationPrompt {
            get {
                return ResourceManager.GetString("MainWindow_InstanceUpdatingSuccessNotificationPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Go to Marketplace.
        /// </summary>
        public static string MainWindow_MarketplaceButtonText {
            get {
                return ResourceManager.GetString("MainWindow_MarketplaceButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Folder.
        /// </summary>
        public static string MainWindow_OpenFolderMenuText {
            get {
                return ResourceManager.GetString("MainWindow_OpenFolderMenuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Play.
        /// </summary>
        public static string MainWindow_PlayMenuText {
            get {
                return ResourceManager.GetString("MainWindow_PlayMenuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Properties.
        /// </summary>
        public static string MainWindow_PropertiesMenuText {
            get {
                return ResourceManager.GetString("MainWindow_PropertiesMenuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Setup.
        /// </summary>
        public static string MainWindow_SetupMenuText {
            get {
                return ResourceManager.GetString("MainWindow_SetupMenuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Everything(mostly) begins here.
        /// </summary>
        public static string MarketplacePortalView_DiscoveryCenterSubtitle {
            get {
                return ResourceManager.GetString("MarketplacePortalView_DiscoveryCenterSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Discovery Center.
        /// </summary>
        public static string MarketplacePortalView_DiscoveryCenterTitle {
            get {
                return ResourceManager.GetString("MarketplacePortalView_DiscoveryCenterTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to News.
        /// </summary>
        public static string MarketplacePortalView_NewsLabelText {
            get {
                return ResourceManager.GetString("MarketplacePortalView_NewsLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Learn More.
        /// </summary>
        public static string MarketplacePortalView_NewsLearnMoreButtonText {
            get {
                return ResourceManager.GetString("MarketplacePortalView_NewsLearnMoreButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        public static string MarketplacePortalView_SearchButtonText {
            get {
                return ResourceManager.GetString("MarketplacePortalView_SearchButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marketplace Portal.
        /// </summary>
        public static string MarketplacePortalView_Title {
            get {
                return ResourceManager.GetString("MarketplacePortalView_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to {0} has been added to the queue.
        /// </summary>
        public static string MarketplaceSearchView_ModpackInstallingNotificationPrompt {
            get {
                return ResourceManager.GetString("MarketplaceSearchView_ModpackInstallingNotificationPrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Loading project information failed.
        /// </summary>
        public static string MarketplaceSearchView_ModpackLoadingDangerNotificationTitle {
            get {
                return ResourceManager.GetString("MarketplaceSearchView_ModpackLoadingDangerNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Website.
        /// </summary>
        public static string MarketplaceSearchView_OpenWebsiteMenuText {
            get {
                return ResourceManager.GetString("MarketplaceSearchView_OpenWebsiteMenuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear Filter.
        /// </summary>
        public static string MarketplaceSearchView_ResetFilterButtonText {
            get {
                return ResourceManager.GetString("MarketplaceSearchView_ResetFilterButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Results:.
        /// </summary>
        public static string MarketplaceSearchView_ResultCountLabelText {
            get {
                return ResourceManager.GetString("MarketplaceSearchView_ResultCountLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to With the keywords in....
        /// </summary>
        public static string MarketplaceSearchView_SearchBarPlaceholder {
            get {
                return ResourceManager.GetString("MarketplaceSearchView_SearchBarPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        public static string MarketplaceSearchView_SearchButtonText {
            get {
                return ResourceManager.GetString("MarketplaceSearchView_SearchButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search modpacks.
        /// </summary>
        public static string MarketplaceSearchView_Title {
            get {
                return ResourceManager.GetString("MarketplaceSearchView_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create instance.
        /// </summary>
        public static string NewInstanceView_CreateButtonText {
            get {
                return ResourceManager.GetString("NewInstanceView_CreateButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Marketplace.
        /// </summary>
        public static string NewInstanceView_DownloadButtonText {
            get {
                return ResourceManager.GetString("NewInstanceView_DownloadButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Write icon failed.
        /// </summary>
        public static string NewInstanceView_IconSavingDangerNotificationTitle {
            get {
                return ResourceManager.GetString("NewInstanceView_IconSavingDangerNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import.
        /// </summary>
        public static string NewInstanceView_ImportButtonText {
            get {
                return ResourceManager.GetString("NewInstanceView_ImportButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import failed.
        /// </summary>
        public static string NewInstanceView_ImportDangerNotificationTitle {
            get {
                return ResourceManager.GetString("NewInstanceView_ImportDangerNotificationTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mod Loader.
        /// </summary>
        public static string NewInstanceView_ModLoaderLabelText {
            get {
                return ResourceManager.GetString("NewInstanceView_ModLoaderLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Name.
        /// </summary>
        public static string NewInstanceView_NameLabelText {
            get {
                return ResourceManager.GetString("NewInstanceView_NameLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Packages.
        /// </summary>
        public static string NewInstanceView_PackageCountLabelText {
            get {
                return ResourceManager.GetString("NewInstanceView_PackageCountLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Select a compressed modpack file to import.
        /// </summary>
        public static string NewInstanceView_RequestFilePrompt {
            get {
                return ResourceManager.GetString("NewInstanceView_RequestFilePrompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Import from file.
        /// </summary>
        public static string NewInstanceView_RequestFileTitle {
            get {
                return ResourceManager.GetString("NewInstanceView_RequestFileTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Or create from.
        /// </summary>
        public static string NewInstanceView_SeparatorLabelText {
            get {
                return ResourceManager.GetString("NewInstanceView_SeparatorLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Create an instance.
        /// </summary>
        public static string NewInstanceView_Title {
            get {
                return ResourceManager.GetString("NewInstanceView_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Version.
        /// </summary>
        public static string NewInstanceView_VersionLabelText {
            get {
                return ResourceManager.GetString("NewInstanceView_VersionLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update packages in bulk.
        /// </summary>
        public static string PackageBulkUpdaterModal_Title {
            get {
                return ResourceManager.GetString("PackageBulkUpdaterModal_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Update Checked.
        /// </summary>
        public static string PackageBulkUpdaterModal_UpdateButtonText {
            get {
                return ResourceManager.GetString("PackageBulkUpdaterModal_UpdateButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Active.
        /// </summary>
        public static string PackageContainer_ActiveMenuText {
            get {
                return ResourceManager.GetString("PackageContainer_ActiveMenuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Batch Update.
        /// </summary>
        public static string PackageContainer_BatchUpdateMenuText {
            get {
                return ResourceManager.GetString("PackageContainer_BatchUpdateMenuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Filter.
        /// </summary>
        public static string PackageContainer_ConditionLabelText {
            get {
                return ResourceManager.GetString("PackageContainer_ConditionLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dependency Graph.
        /// </summary>
        public static string PackageContainer_DependencyGraphMenuText {
            get {
                return ResourceManager.GetString("PackageContainer_DependencyGraphMenuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Explort List.
        /// </summary>
        public static string PackageContainer_ExportListMenuText {
            get {
                return ResourceManager.GetString("PackageContainer_ExportListMenuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enter filter string....
        /// </summary>
        public static string PackageContainer_FilterBarPlaceholder {
            get {
                return ResourceManager.GetString("PackageContainer_FilterBarPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Get More.
        /// </summary>
        public static string PackageContainer_GetMoreButtonText {
            get {
                return ResourceManager.GetString("PackageContainer_GetMoreButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Open Website.
        /// </summary>
        public static string PackageContainer_OpenWebsiteMenuText {
            get {
                return ResourceManager.GetString("PackageContainer_OpenWebsiteMenuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove.
        /// </summary>
        public static string PackageContainer_RemoveMenuText {
            get {
                return ResourceManager.GetString("PackageContainer_RemoveMenuText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Results.
        /// </summary>
        public static string PackageContainer_ResultCountLabelText {
            get {
                return ResourceManager.GetString("PackageContainer_ResultCountLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Source.
        /// </summary>
        public static string PackageContainer_SourceLabelText {
            get {
                return ResourceManager.GetString("PackageContainer_SourceLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Local.
        /// </summary>
        public static string PackageContainer_SourceLocalText {
            get {
                return ResourceManager.GetString("PackageContainer_SourceLocalText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Original.
        /// </summary>
        public static string PackageContainer_SourceOriginalText {
            get {
                return ResourceManager.GetString("PackageContainer_SourceOriginalText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Status.
        /// </summary>
        public static string PackageContainer_StatusLabelText {
            get {
                return ResourceManager.GetString("PackageContainer_StatusLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tags.
        /// </summary>
        public static string PackageContainer_TagLabelText {
            get {
                return ResourceManager.GetString("PackageContainer_TagLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Type.
        /// </summary>
        public static string PackageContainer_TypeLabelText {
            get {
                return ResourceManager.GetString("PackageContainer_TypeLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Add.
        /// </summary>
        public static string PackageExplorerView_AddLabelText {
            get {
                return ResourceManager.GetString("PackageExplorerView_AddLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Collect.
        /// </summary>
        public static string PackageExplorerView_CollectButtonText {
            get {
                return ResourceManager.GetString("PackageExplorerView_CollectButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dismiss.
        /// </summary>
        public static string PackageExplorerView_DismissButtonText {
            get {
                return ResourceManager.GetString("PackageExplorerView_DismissButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Empty.
        /// </summary>
        public static string PackageExplorerView_EmptyLabelText {
            get {
                return ResourceManager.GetString("PackageExplorerView_EmptyLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Modify.
        /// </summary>
        public static string PackageExplorerView_ModifyLabelText {
            get {
                return ResourceManager.GetString("PackageExplorerView_ModifyLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Pending.
        /// </summary>
        public static string PackageExplorerView_PendingLabelText {
            get {
                return ResourceManager.GetString("PackageExplorerView_PendingLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Remove.
        /// </summary>
        public static string PackageExplorerView_RemoveLabelText {
            get {
                return ResourceManager.GetString("PackageExplorerView_RemoveLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search by names and names....
        /// </summary>
        public static string PackageExplorerView_SearchBarPlaceholder {
            get {
                return ResourceManager.GetString("PackageExplorerView_SearchBarPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Search.
        /// </summary>
        public static string PackageExplorerView_SearchButtonText {
            get {
                return ResourceManager.GetString("PackageExplorerView_SearchButtonText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All the mods will be replaced. Configs will be replaced with new ones..
        /// </summary>
        public static string ReferenceVersionPickerDialog_Prompt {
            get {
                return ResourceManager.GetString("ReferenceVersionPickerDialog_Prompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Choose a version to upgrade/downgrade to.
        /// </summary>
        public static string ReferenceVersionPickerDialog_Title {
            get {
                return ResourceManager.GetString("ReferenceVersionPickerDialog_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Alpha.
        /// </summary>
        public static string ReleaseType_Alpha {
            get {
                return ResourceManager.GetString("ReleaseType_Alpha", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Beta.
        /// </summary>
        public static string ReleaseType_Beta {
            get {
                return ResourceManager.GetString("ReleaseType_Beta", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Release.
        /// </summary>
        public static string ReleaseType_Release {
            get {
                return ResourceManager.GetString("ReleaseType_Release", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Data Pack.
        /// </summary>
        public static string ResourceKind_DataPack {
            get {
                return ResourceManager.GetString("ResourceKind_DataPack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mod.
        /// </summary>
        public static string ResourceKind_Mod {
            get {
                return ResourceManager.GetString("ResourceKind_Mod", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Modpack.
        /// </summary>
        public static string ResourceKind_Modpack {
            get {
                return ResourceManager.GetString("ResourceKind_Modpack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Resource Pack.
        /// </summary>
        public static string ResourceKind_ResourcePack {
            get {
                return ResourceManager.GetString("ResourceKind_ResourcePack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Shader Pack.
        /// </summary>
        public static string ResourceKind_ShaderPack {
            get {
                return ResourceManager.GetString("ResourceKind_ShaderPack", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to World.
        /// </summary>
        public static string ResourceKind_World {
            get {
                return ResourceManager.GetString("ResourceKind_World", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Code:.
        /// </summary>
        public static string SafeLock_CodeLabelText {
            get {
                return ResourceManager.GetString("SafeLock_CodeLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Repeat.
        /// </summary>
        public static string SafeLock_RepeatLabelText {
            get {
                return ResourceManager.GetString("SafeLock_RepeatLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Safe Lock.
        /// </summary>
        public static string SafeLock_Title {
            get {
                return ResourceManager.GetString("SafeLock_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Accent Color.
        /// </summary>
        public static string SettingsView_AccentColorLabelText {
            get {
                return ResourceManager.GetString("SettingsView_AccentColorLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Acrylic.
        /// </summary>
        public static string SettingsView_BackgroundStyleAcrylicText {
            get {
                return ResourceManager.GetString("SettingsView_BackgroundStyleAcrylicText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Auto.
        /// </summary>
        public static string SettingsView_BackgroundStyleAutoText {
            get {
                return ResourceManager.GetString("SettingsView_BackgroundStyleAutoText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Blur.
        /// </summary>
        public static string SettingsView_BackgroundStyleBlurText {
            get {
                return ResourceManager.GetString("SettingsView_BackgroundStyleBlurText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Background Style.
        /// </summary>
        public static string SettingsView_BackgroundStyleLabelText {
            get {
                return ResourceManager.GetString("SettingsView_BackgroundStyleLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mica.
        /// </summary>
        public static string SettingsView_BackgroundStyleMicaText {
            get {
                return ResourceManager.GetString("SettingsView_BackgroundStyleMicaText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to None.
        /// </summary>
        public static string SettingsView_BackgroundStyleNoneText {
            get {
                return ResourceManager.GetString("SettingsView_BackgroundStyleNoneText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Corner Radius.
        /// </summary>
        public static string SettingsView_CornerStyleLabelText {
            get {
                return ResourceManager.GetString("SettingsView_CornerStyleLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The look, the language, the font.
        /// </summary>
        public static string SettingsView_DisplaySubtitle {
            get {
                return ResourceManager.GetString("SettingsView_DisplaySubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Display and Appearance.
        /// </summary>
        public static string SettingsView_DisplayTitle {
            get {
                return ResourceManager.GetString("SettingsView_DisplayTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Font.
        /// </summary>
        public static string SettingsView_FontLabelText {
            get {
                return ResourceManager.GetString("SettingsView_FontLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to The listing configuration can be override by the instance individually.
        /// </summary>
        public static string SettingsView_GameDefaultsSubtitle {
            get {
                return ResourceManager.GetString("SettingsView_GameDefaultsSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Game Defaults.
        /// </summary>
        public static string SettingsView_GameDefaultsTitle {
            get {
                return ResourceManager.GetString("SettingsView_GameDefaultsTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Java 11 Home.
        /// </summary>
        public static string SettingsView_Java11LabelText {
            get {
                return ResourceManager.GetString("SettingsView_Java11LabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Java 17 Home.
        /// </summary>
        public static string SettingsView_Java17LabelText {
            get {
                return ResourceManager.GetString("SettingsView_Java17LabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Java 21 Home.
        /// </summary>
        public static string SettingsView_Java21LabelText {
            get {
                return ResourceManager.GetString("SettingsView_Java21LabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Java 8 Home.
        /// </summary>
        public static string SettingsView_Java8LabelText {
            get {
                return ResourceManager.GetString("SettingsView_Java8LabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Java Additional Arguments.
        /// </summary>
        public static string SettingsView_JavaAdditionalArgumentsLabelText {
            get {
                return ResourceManager.GetString("SettingsView_JavaAdditionalArgumentsLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Less is more.
        /// </summary>
        public static string SettingsView_JavaAdditionalArgumentsPlaceholder {
            get {
                return ResourceManager.GetString("SettingsView_JavaAdditionalArgumentsPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Java Max Memory.
        /// </summary>
        public static string SettingsView_JavaMaxMemoryLabelText {
            get {
                return ResourceManager.GetString("SettingsView_JavaMaxMemoryLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Bigger is better.
        /// </summary>
        public static string SettingsView_JavaMaxMemoryPlaceholder {
            get {
                return ResourceManager.GetString("SettingsView_JavaMaxMemoryPlaceholder", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to MiB.
        /// </summary>
        public static string SettingsView_JavaMaxMemoryUnitText {
            get {
                return ResourceManager.GetString("SettingsView_JavaMaxMemoryUnitText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Instance will select the best match while launching.
        /// </summary>
        public static string SettingsView_JavaSubtitle {
            get {
                return ResourceManager.GetString("SettingsView_JavaSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Java Presents.
        /// </summary>
        public static string SettingsView_JavaTitle {
            get {
                return ResourceManager.GetString("SettingsView_JavaTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Language.
        /// </summary>
        public static string SettingsView_LanguageLabelText {
            get {
                return ResourceManager.GetString("SettingsView_LanguageLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sidebar Placement.
        /// </summary>
        public static string SettingsView_SidebarPlacementLabelText {
            get {
                return ResourceManager.GetString("SettingsView_SidebarPlacementLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Left.
        /// </summary>
        public static string SettingsView_SidebarPlacementLeftText {
            get {
                return ResourceManager.GetString("SettingsView_SidebarPlacementLeftText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Right.
        /// </summary>
        public static string SettingsView_SidebarPlacementRightText {
            get {
                return ResourceManager.GetString("SettingsView_SidebarPlacementRightText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Activate.
        /// </summary>
        public static string SettingsView_SuperPowerLabelText {
            get {
                return ResourceManager.GetString("SettingsView_SuperPowerLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ....
        /// </summary>
        public static string SettingsView_SuperPowerSubtitle {
            get {
                return ResourceManager.GetString("SettingsView_SuperPowerSubtitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Super Power.
        /// </summary>
        public static string SettingsView_SuperPowerTitle {
            get {
                return ResourceManager.GetString("SettingsView_SuperPowerTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Dark.
        /// </summary>
        public static string SettingsView_ThemeVariantDarkText {
            get {
                return ResourceManager.GetString("SettingsView_ThemeVariantDarkText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Theme Variant.
        /// </summary>
        public static string SettingsView_ThemeVariantLabelText {
            get {
                return ResourceManager.GetString("SettingsView_ThemeVariantLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Light.
        /// </summary>
        public static string SettingsView_ThemeVariantLightText {
            get {
                return ResourceManager.GetString("SettingsView_ThemeVariantLightText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to System.
        /// </summary>
        public static string SettingsView_ThemeVariantSystemText {
            get {
                return ResourceManager.GetString("SettingsView_ThemeVariantSystemText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Application Settings.
        /// </summary>
        public static string SettingsView_Title {
            get {
                return ResourceManager.GetString("SettingsView_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Title Bar Visibility.
        /// </summary>
        public static string SettingsView_TitleBarVisibilityLabelText {
            get {
                return ResourceManager.GetString("SettingsView_TitleBarVisibilityLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Height.
        /// </summary>
        public static string SettingsView_WindowHeightLabelText {
            get {
                return ResourceManager.GetString("SettingsView_WindowHeightLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Window Initial Size.
        /// </summary>
        public static string SettingsView_WindowInitialSizeLabelText {
            get {
                return ResourceManager.GetString("SettingsView_WindowInitialSizeLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Width.
        /// </summary>
        public static string SettingsView_WindowWidthLabelText {
            get {
                return ResourceManager.GetString("SettingsView_WindowWidthLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No Entries.
        /// </summary>
        public static string Shared_EmptyListLabelText {
            get {
                return ResourceManager.GetString("Shared_EmptyListLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to EXTERNAL LINK TO.
        /// </summary>
        public static string Shared_ExternalLinkLabelText {
            get {
                return ResourceManager.GetString("Shared_ExternalLinkLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fetching....
        /// </summary>
        public static string Shared_FetchingLabelText {
            get {
                return ResourceManager.GetString("Shared_FetchingLabelText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Just write something down below....
        /// </summary>
        public static string UserInputDialog_Prompt {
            get {
                return ResourceManager.GetString("UserInputDialog_Prompt", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Get something entered.
        /// </summary>
        public static string UserInputDialog_Title {
            get {
                return ResourceManager.GetString("UserInputDialog_Title", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Fri.
        /// </summary>
        public static string Week_Friday {
            get {
                return ResourceManager.GetString("Week_Friday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Mon.
        /// </summary>
        public static string Week_Monday {
            get {
                return ResourceManager.GetString("Week_Monday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sat.
        /// </summary>
        public static string Week_Saturday {
            get {
                return ResourceManager.GetString("Week_Saturday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Sun.
        /// </summary>
        public static string Week_Sunday {
            get {
                return ResourceManager.GetString("Week_Sunday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Thu.
        /// </summary>
        public static string Week_Thursday {
            get {
                return ResourceManager.GetString("Week_Thursday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Tue.
        /// </summary>
        public static string Week_Tuesday {
            get {
                return ResourceManager.GetString("Week_Tuesday", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Wed.
        /// </summary>
        public static string Week_Wednesday {
            get {
                return ResourceManager.GetString("Week_Wednesday", resourceCulture);
            }
        }
    }
}
