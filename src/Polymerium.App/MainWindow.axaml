<husk:AppWindow
    x:Class="Polymerium.App.MainWindow"
    xmlns="https://github.com/avaloniaui"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:controls="using:Polymerium.App.Controls"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
    xmlns:icons="https://github.com/MahApps/IconPacks.Avalonia"
    xmlns:local="using:Polymerium.App"
    xmlns:m="using:Polymerium.App.Models"
    xmlns:v="using:Polymerium.App.Views"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:fie="clr-namespace:FluentIcons.Avalonia.MarkupExtensions;assembly=FluentIcons.Avalonia"
    xmlns:lang="https://github.com/d3ara1n/Polymerium/Languages"
    ExtendClientAreaChromeHints="NoChrome"
    ExtendClientAreaToDecorationsHint="True"
    Title="{x:Static local:Program.Brand}"
    Width="1111"
    Height="666"
    MinWidth="600"
    MinHeight="460"
    d:DesignHeight="450"
    d:DesignWidth="800"
    mc:Ignorable="d" x:DataType="local:MainWindowContext">
    <husk:AppWindow.Styles>
        <Style Selector="husk|Dialog">
            <Setter Property="PrimaryText" Value="{x:Static lang:Resources.Dialog_ConfirmButtonText}" />
            <Setter Property="SecondaryText" Value="{x:Static lang:Resources.Dialog_CancelButtonText}" />
        </Style>
    </husk:AppWindow.Styles>
    <Grid Name="Container">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="83*" />
            <ColumnDefinition Width="27*"
                              MinWidth="200"
                              MaxWidth="400" />
        </Grid.ColumnDefinitions>
        <DockPanel x:Name="Main" Grid.Column="0">
            <Panel>
                <StackPanel VerticalAlignment="Center"
                            Opacity="{Binding #Root.Content, Converter={x:Static husk:OpacityConverters.IsNull}}">
                    <StackPanel.Transitions>
                        <Transitions>
                            <DoubleTransition
                                Easing="CubicEaseInOut"
                                Property="Opacity"
                                Duration="{StaticResource ControlFasterAnimationDuration}" />
                        </Transitions>
                    </StackPanel.Transitions>
                    <StackPanel.Effect>
                        <DropShadowEffect BlurRadius="6" Opacity="0.1" />
                    </StackPanel.Effect>
                    <Image
                        Width="128"
                        Height="128"
                        HorizontalAlignment="Center"
                        Source="/Assets/Icons/Bird.png" />
                    <TextBlock
                        Margin="48,12,48,12"
                        FontSize="24"
                        FontWeight="{StaticResource ControlStrongFontWeight}"
                        Text="{x:Static lang:Resources.MainWindow_BackgroundText}"
                        TextAlignment="Center"
                        TextWrapping="WrapWithOverflow" />
                </StackPanel>
                <!-- NOTE: ClipToBounds=False 是为了让 Page.Effect 不被隐藏，为了实现左右布局对换而不得不去掉了 Page.Margin.Left|Right -->
                <husk:Frame x:Name="Root" CanGoBackOutOfStack="True" />
            </Panel>
        </DockPanel>
        <DockPanel x:Name="Sidebar" Grid.Column="1">
            <DockPanel x:Name="TitleBar" DockPanel.Dock="Top"
                       IsVisible="{Binding $parent[local:MainWindow].IsTitleBarVisible, Mode=OneWay}">
                <StackPanel DockPanel.Dock="Right">
                    <husk:ButtonGroup BorderThickness="0">
                        <Button
                            x:Name="MinimizeButton"
                            Padding="10"
                            Click="MinimizeButton_OnClick">
                            <icons:PackIconLucide
                                Width="10"
                                Height="10"
                                Kind="Minus" />
                        </Button>
                        <Button
                            x:Name="ToggleMaximizeButton"
                            Padding="10"
                            Click="ToggleMaximizeButton_OnClick">
                            <husk:SwitchPresenter Value="{Binding $parent[husk:AppWindow].IsMaximized,Mode=OneWay}"
                                                  TargetType="x:Boolean">
                                <husk:SwitchCase Value="True">
                                    <icons:PackIconLucide
                                        Width="10"
                                        Height="10"
                                        Kind="SquareSquare" />
                                </husk:SwitchCase>
                                <husk:SwitchCase Value="False">
                                    <icons:PackIconLucide
                                        Width="10"
                                        Height="10"
                                        Kind="Square" />
                                </husk:SwitchCase>
                            </husk:SwitchPresenter>
                        </Button>
                        <Button
                            x:Name="CloseButton"
                            Padding="10"
                            Click="CloseButton_OnClick">
                            <icons:PackIconLucide
                                Width="10"
                                Height="10"
                                Kind="X" />
                        </Button>
                    </husk:ButtonGroup>
                </StackPanel>
                <StackPanel
                    Margin="12,12,12,0"
                    IsHitTestVisible="False"
                    Orientation="Horizontal"
                    Spacing="6">
                    <StackPanel.Effect>
                        <DropShadowEffect BlurRadius="6" Opacity="0.1" />
                    </StackPanel.Effect>
                    <icons:PackIconLucide Height="{StaticResource MediumFontSize}"
                                          Width="{StaticResource MediumFontSize}" Kind="Squirrel"
                                          VerticalAlignment="Center" />
                    <TextBlock
                        VerticalAlignment="Center"
                        FontSize="{StaticResource SmallFontSize}"
                        Text="{Binding $parent[Window].Title, Mode=OneWay}" />
                </StackPanel>
            </DockPanel>
            <Grid
                Margin="9"
                ColumnDefinitions="Auto,*,Auto"
                RowDefinitions="Auto,Auto,Auto,Auto,*,Auto,Auto" RowSpacing="8" ColumnSpacing="8">
                <Button
                    Grid.Row="0"
                    Grid.Column="0"
                    Grid.ColumnSpan="2"
                    Height="48"
                    Command="{Binding NavigateCommand}"
                    CommandParameter="{x:Type v:LandingView}"
                    Theme="{StaticResource OutlineButtonTheme}">
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <icons:PackIconLucide
                            Width="18"
                            Height="18"
                            VerticalAlignment="Center"
                            Kind="Ghost" />
                        <TextBlock VerticalAlignment="Center"
                                   Text="{x:Static lang:Resources.MainWindow_HomeButtonText}" />
                    </StackPanel>
                </Button>
                <Button
                    Grid.Row="0"
                    Grid.Column="2"
                    Width="48"
                    Height="48"
                    Command="{Binding NavigateCommand}"
                    CommandParameter="{x:Type v:NewInstanceView}"
                    Theme="{StaticResource OutlineButtonTheme}">
                    <icons:PackIconLucide
                        Width="18"
                        Height="18"
                        Kind="PackagePlus" />
                </Button>
                <Button
                    Grid.Row="1"
                    Grid.Column="0"
                    Grid.ColumnSpan="3"
                    Height="36"
                    Classes="Primary"
                    Command="{Binding NavigateCommand}"
                    CommandParameter="{x:Type v:MarketplacePortalView}">
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <icons:PackIconLucide
                            Width="18"
                            Height="18"
                            Kind="Store" />
                        <TextBlock VerticalAlignment="Center"
                                   Text="{x:Static lang:Resources.MainWindow_MarketplaceButtonText}" />
                    </StackPanel>
                </Button>
                <husk:Divider
                    Grid.Row="2"
                    Grid.Column="0"
                    Grid.ColumnSpan="3" />
                <!-- <AutoCompleteBox/> -->
                <TextBox
                    Grid.Row="3"
                    Grid.Column="0"
                    Grid.ColumnSpan="3"
                    Text="{Binding FilterText, Mode=TwoWay}"
                    Watermark="{x:Static lang:Resources.MainWindow_InstanceFilterPlaceholder}">
                    <TextBox.InnerRightContent>
                        <Button
                            IsVisible="{Binding $parent[TextBox].Text,Converter={x:Static StringConverters.IsNotNullOrEmpty}}"
                            Command="{Binding $parent[TextBox].Clear}"
                            Content="{fie:SymbolIcon Symbol=Dismiss,FontSize={StaticResource MediumFontSize}}"
                            Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                    </TextBox.InnerRightContent>
                </TextBox>
                <Panel
                    Grid.Row="4"
                    Grid.Column="0"
                    Grid.ColumnSpan="3">
                    <StackPanel VerticalAlignment="Center"
                                IsVisible="{Binding View.Count,Converter={x:Static husk:NumberConverters.IsZero},FallbackValue=True}"
                                Spacing="8">
                        <icons:PackIconLucide Kind="Box" Height="{StaticResource ExtraLargeFontSize}"
                                              Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                              Width="{StaticResource ExtraLargeFontSize}" HorizontalAlignment="Center" />
                        <TextBlock Text="{x:Static lang:Resources.Shared_EmptyListLabelText}"
                                   FontSize="{StaticResource LargeFontSize}"
                                   Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                                   HorizontalAlignment="Center" />
                    </StackPanel>
                    <ScrollViewer
                        Margin="0,0,-8,0">
                        <ItemsControl Margin="0,0,8,0" ItemsSource="{Binding View}"
                                      ClipToBounds="False">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate DataType="m:InstanceEntryModel">
                                    <controls:InstanceEntryButton
                                        Command="{Binding $parent[local:MainWindow].((local:MainWindowContext)DataContext).ViewInstanceCommand,FallbackValue={x:Null}}"
                                        CommandParameter="{Binding Basic.Key}"
                                        State="{Binding State,Mode=OneWay}">
                                        <controls:InstanceEntryButton.ContextFlyout>
                                            <MenuFlyout>
                                                <MenuItem Header="{x:Static lang:Resources.MainWindow_PlayMenuText}"
                                                          Icon="{fie:SymbolIcon Symbol=Rocket,FontSize={StaticResource MediumFontSize}}"
                                                          Command="{Binding $parent[local:MainWindow].((local:MainWindowContext)DataContext).PlayCommand,FallbackValue={x:Null}}"
                                                          CommandParameter="{Binding Basic.Key}" />
                                                <MenuItem Header="{x:Static lang:Resources.MainWindow_DeployMenuText}"
                                                          Icon="{fie:SymbolIcon Symbol=SettingsCogMultiple,FontSize={StaticResource MediumFontSize}}"
                                                          Command="{Binding $parent[local:MainWindow].((local:MainWindowContext)DataContext).DeployCommand,FallbackValue={x:Null}}"
                                                          CommandParameter="{Binding Basic.Key}" />
                                                <MenuItem Header="-" />
                                                <MenuItem
                                                    Header="{x:Static lang:Resources.MainWindow_OpenFolderMenuText}"
                                                    Icon="{fie:SymbolIcon Symbol=FolderOpen,FontSize={StaticResource MediumFontSize}}"
                                                    Command="{Binding $parent[local:MainWindow].((local:MainWindowContext)DataContext).OpenFolderCommand,FallbackValue={x:Null}}"
                                                    CommandParameter="{Binding Basic.Key}" />
                                                <MenuItem Header="-" />
                                                <MenuItem Header="{x:Static lang:Resources.MainWindow_SetupMenuText}"
                                                          Icon="{fie:SymbolIcon Symbol=BoxMultiple,FontSize={StaticResource MediumFontSize}}"
                                                          Command="{Binding $parent[local:MainWindow].((local:MainWindowContext)DataContext).GotoSetupCommand,FallbackValue={x:Null}}"
                                                          CommandParameter="{Binding Basic.Key}" />
                                                <MenuItem
                                                    Header="{x:Static lang:Resources.MainWindow_PropertiesMenuText}"
                                                    Icon="{fie:SymbolIcon Symbol=WrenchScrewdriver,FontSize={StaticResource MediumFontSize}}"
                                                    Command="{Binding $parent[local:MainWindow].((local:MainWindowContext)DataContext).GotoPropertiesCommand,FallbackValue={x:Null}}"
                                                    CommandParameter="{Binding Basic.Key}" />
                                            </MenuFlyout>
                                        </controls:InstanceEntryButton.ContextFlyout>
                                    </controls:InstanceEntryButton>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                            <ItemsControl.ItemsPanel>
                                <ItemsPanelTemplate>
                                    <StackPanel Spacing="4" />
                                </ItemsPanelTemplate>
                            </ItemsControl.ItemsPanel>
                        </ItemsControl>
                    </ScrollViewer>
                </Panel>
                <husk:Divider
                    Grid.Row="5"
                    Grid.Column="0"
                    Grid.ColumnSpan="3" />
                <Button
                    Grid.Row="6"
                    Grid.Column="0"
                    Command="{Binding NavigateCommand}"
                    DockPanel.Dock="Left"
                    CommandParameter="{x:Type v:SettingsView}"
                    Theme="{StaticResource GhostButtonTheme}">
                    <icons:PackIconLucide Height="18" Kind="Settings" />
                </Button>
                <Button
                    Grid.Row="6"
                    Grid.Column="1"
                    Grid.ColumnSpan="2"
                    Command="{Binding NavigateCommand}"
                    CommandParameter="{x:Type v:AccountsView}"
                    Theme="{StaticResource OutlineButtonTheme}">
                    <TextBlock Text="{x:Static lang:Resources.MainWindow_AccountButtonText}" />
                </Button>
            </Grid>
        </DockPanel>
    </Grid>
</husk:AppWindow>