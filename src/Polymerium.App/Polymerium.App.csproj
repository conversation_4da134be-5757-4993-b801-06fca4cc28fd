<Project Sdk="Microsoft.NET.Sdk">
    <PropertyGroup>
        <OutputType>WinExe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
        <ApplicationManifest>app.manifest</ApplicationManifest>
        <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
        <ApplicationIcon>Assets\Logo.ico</ApplicationIcon>
        <LangVersion>preview</LangVersion>

        <!-- GitVersion properties -->
        <GenerateAssemblyInfo>true</GenerateAssemblyInfo>
        <UseGitVersionTask>true</UseGitVersionTask>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Avalonia" Version="11.3.2"/>
        <PackageReference Include="Avalonia.Desktop" Version="11.3.2"/>
        <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
        <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="11.3.2"/>
        <PackageReference Include="Avalonia.Svg" Version="11.3.0"/>
        <PackageReference Include="CommunityToolkit.Mvvm" Version="8.4.0"/>
        <PackageReference Include="CsvHelper" Version="33.1.0"/>
        <PackageReference Include="DynamicData" Version="9.4.1"/>
        <PackageReference Include="FluentIcons.Avalonia" Version="1.1.303"/>
        <PackageReference Include="FreeSql.Provider.SqliteCore" Version="3.5.212" />
        <PackageReference Include="GitVersion.MsBuild" Version="6.3.0">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Humanizer" Version="3.0.0-beta.96"/>
        <PackageReference Include="IconPacks.Avalonia.Lucide" Version="1.1.0" />
        <PackageReference Include="JetBrains.Annotations" Version="2025.2.0" />
        <PackageReference Include="LiveChartsCore.SkiaSharpView.Avalonia" Version="2.0.0-rc5.4"/>
        <PackageReference Include="Markdown.Avalonia" Version="11.0.3-a1"/>
        <PackageReference Include="Microsoft.Extensions.Caching.Memory" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.7"/>
        <PackageReference Include="NeoSmart.Caching.Sqlite.AspNetCore" Version="9.0.1"/>
        <PackageReference Include="Semver" Version="3.0.0"/>
        <PackageReference Include="AsyncImageLoader.Avalonia" Version="3.3.0"/>
        <PackageReference Include="HotAvalonia" Version="3.0.0" PrivateAssets="All"/>
        <PackageReference Include="Velopack" Version="0.0.1298"/>
    </ItemGroup>

    <ItemGroup>
        <None Update="appsettings.json">
            <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
        </None>
    </ItemGroup>

    <ItemGroup>
        <AvaloniaResource Include="Assets\**"/>
    </ItemGroup>

    <ItemGroup>
        <EmbeddedResource Update="Properties\Resources.resx">
            <Generator>PublicResXFileCodeGenerator</Generator>
            <LastGenOutput>Resources.Designer.cs</LastGenOutput>
        </EmbeddedResource>
    </ItemGroup>

    <ItemGroup>
        <Compile Update="Properties\Resources.Designer.cs">
            <DesignTime>True</DesignTime>
            <AutoGen>True</AutoGen>
            <DependentUpon>Resources.resx</DependentUpon>
        </Compile>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\submodules\Huskui.Avalonia\src\Huskui.Avalonia\Huskui.Avalonia.csproj"/>
        <ProjectReference Include="..\Polymerium.Trident\Polymerium.Trident.csproj"/>
    </ItemGroup>
</Project>
