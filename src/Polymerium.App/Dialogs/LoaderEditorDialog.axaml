<husk:Dialog xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia"
             xmlns:fi="clr-namespace:FluentIcons.Avalonia;assembly=FluentIcons.Avalonia"
             xmlns:local="clr-namespace:Polymerium.App.Dialogs"
             xmlns:m="clr-namespace:Polymerium.App.Models"
             mc:Ignorable="d" d:DesignWidth="460" IsPrimaryButtonVisible="True"
             x:Class="Polymerium.App.Dialogs.LoaderEditorDialog">
    <husk:SwitchPresenter
        Value="{Binding $parent[local:LoaderEditorDialog].SelectedLoader,Converter={x:Static ObjectConverters.IsNotNull},Mode=OneWay}"
        TargetType="x:Boolean">
        <husk:SwitchCase Value="True">
            <Grid RowDefinitions="*,12,Auto,4,Auto">
                <Border Grid.Row="0" Background="{StaticResource ControlTranslucentFullBackgroundBrush}"
                        CornerRadius="{StaticResource MediumCornerRadius}">
                    <Grid ColumnDefinitions="*,4,Auto" Margin="3">
                        <Border Grid.Column="0" Background="{StaticResource OverlaySolidBackgroundBrush}"
                                CornerRadius="{StaticResource SmallCornerRadius}">
                            <Grid ColumnDefinitions="Auto,*">
                                <Border Grid.Column="0"
                                        Background="{StaticResource ControlTranslucentHalfBackgroundBrush}"
                                        CornerRadius="{Binding Source={StaticResource SmallCornerRadius},Converter={x:Static husk:CornerRadiusConverters.Left}}">
                                    <Image
                                        Source="{Binding $parent[local:LoaderEditorDialog].Loader.Thumbnail,FallbackValue={x:Null}}"
                                        Height="36" Width="36" Margin="12" />
                                </Border>
                                <StackPanel Grid.Column="1" Margin="16,0" VerticalAlignment="Center">
                                    <TextBlock
                                        Text="{Binding $parent[local:LoaderEditorDialog].Loader.Display,FallbackValue=Loader}"
                                        FontSize="{StaticResource LargeFontSize}"
                                        FontWeight="{StaticResource ControlStrongFontWeight}" />
                                    <TextBlock
                                        Text="{Binding $parent[local:LoaderEditorDialog].Loader.Id,FallbackValue=Identity}"
                                        Foreground="{StaticResource ControlSecondaryForegroundBrush}" />
                                </StackPanel>
                            </Grid>
                        </Border>
                        <Button Grid.Column="2" Theme="{StaticResource GhostButtonTheme}" Classes="Small"
                                Click="RemoveButton_OnClick" IsCancel="True">
                            <fi:SymbolIcon Symbol="Dismiss" FontSize="{StaticResource MediumFontSize}"
                                           VerticalAlignment="Center" />
                        </Button>
                    </Grid>
                </Border>
                <TextBlock Grid.Row="2" Text="More Versions" />
                <husk:LazyContainer Grid.Row="4" Source="{Binding $parent[local:LoaderEditorDialog].LazyVersions}"
                                    MinHeight="36">
                    <husk:LazyContainer.SourceTemplate>
                        <DataTemplate DataType="m:LoaderCandidateVersionCollectionModel">
                            <ComboBox
                                SelectedValue="{Binding $parent[local:LoaderEditorDialog].SelectedVersion,Mode=TwoWay}"
                                ItemsSource="{Binding Versions}" SelectedValueBinding="{Binding Version}">
                                <ComboBox.ItemTemplate>
                                    <DataTemplate DataType="m:LoaderCandidateVersionModel">
                                        <husk:SwitchPresenter Value="{Binding IsRecommended}" TargetType="x:Boolean">
                                            <husk:SwitchCase Value="True">
                                                <TextBlock Text="{Binding Version}"
                                                           Foreground="{StaticResource ControlAccentForegroundBrush}"
                                                           VerticalAlignment="Center" />
                                            </husk:SwitchCase>
                                            <husk:SwitchCase Value="False">
                                                <TextBlock Text="{Binding Version}" VerticalAlignment="Center" />
                                            </husk:SwitchCase>
                                        </husk:SwitchPresenter>
                                    </DataTemplate>
                                </ComboBox.ItemTemplate>
                            </ComboBox>
                        </DataTemplate>
                    </husk:LazyContainer.SourceTemplate>
                    <husk:LazyContainer.BadContent>
                        <ComboBox IsEnabled="False" SelectedIndex="0">
                            <ComboBoxItem Content="(Network unreachable)" />
                        </ComboBox>
                    </husk:LazyContainer.BadContent>
                </husk:LazyContainer>
            </Grid>
        </husk:SwitchCase>
        <husk:SwitchCase Value="False">
            <Grid RowDefinitions="Auto,24,Auto" Margin="24,32,24,12">
                <StackPanel Grid.Row="0" Spacing="6">
                    <fi:SymbolIcon Symbol="VehicleTractor" FontSize="{StaticResource ExtraLargeFontSize}"
                                   HorizontalAlignment="Center" />
                    <TextBlock Text="No loader selected yet..." HorizontalAlignment="Center" />
                    <TextBlock Text="Mods will not function without a loader."
                               Foreground="{StaticResource ControlSecondaryForegroundBrush}"
                               HorizontalAlignment="Center" />
                </StackPanel>
                <Button Grid.Row="2" HorizontalAlignment="Center" Name="AddLoaderButton"
                        Click="AddLoaderButton_OnClick">
                    <StackPanel Orientation="Horizontal" Spacing="8">
                        <fi:SymbolIcon Symbol="Add" FontSize="{StaticResource MediumFontSize}" />
                        <TextBlock Text="Add Loader" />
                    </StackPanel>
                </Button>
            </Grid>
        </husk:SwitchCase>
    </husk:SwitchPresenter>
</husk:Dialog>