using Polymerium.App.Facilities;
using Polymerium.App.Services;
using Polymerium.App.Widgets;
using Polymerium.Trident.Services;

namespace Polymerium.App.ViewModels;

public class InstanceWidgetsViewModel(
    ViewBag bag,
    InstanceManager instanceManager,
    ProfileManager profileManager,
    WidgetHostService widgetHostService) : InstanceViewModelBase(bag, instanceManager, profileManager)
{
    public WidgetBase[] Widgets { get; } =
    [
        new NoteWidget { Context = widgetHostService.GetOrCreateContext(nameof(NoteWidget)) },
        new NetworkCheckerWidget { Context = widgetHostService.GetOrCreateContext(nameof(NetworkCheckerWidget)) },
        new DummyWidget
        {
            Context = widgetHostService.GetOrCreateContext(nameof(DummyWidget)), Title = "Log Viewer"
        },
        new DummyWidget
        {
            Context = widgetHostService.GetOrCreateContext(nameof(DummyWidget)), Title = "Nbt Editor"
        },
        new DummyWidget { Context = widgetHostService.GetOrCreateContext(nameof(DummyWidget)), Title = "IDK" }
    ];
}