<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <LangVersion>default</LangVersion>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\Trident.Abstractions\Trident.Abstractions.csproj"/>
    </ItemGroup>

    <ItemGroup>
        <PackageReference Include="IBuilder" Version="0.3.0"/>
        <PackageReference Include="JetBrains.Annotations" Version="2024.3.0"/>
        <PackageReference Include="Microsoft.Extensions.Caching.Abstractions" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.Http" Version="9.0.7"/>
        <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="9.0.7"/>
        <PackageReference Include="Mime-Detective" Version="25.4.25"/>
        <PackageReference Include="ObservableCollections" Version="3.3.4"/>
        <PackageReference Include="Refit" Version="8.0.0"/>
        <PackageReference Include="Refit.HttpClientFactory" Version="8.0.0"/>
        <PackageReference Include="ReverseMarkdown" Version="4.7.0"/>
        <PackageReference Include="Semver" Version="3.0.0"/>
        <PackageReference Include="System.Net.Http.Json" Version="10.0.0-preview.5.25277.114"/>
        <PackageReference Include="System.Text.Json" Version="10.0.0-preview.5.25277.114"/>
    </ItemGroup>

    <ItemGroup>
        <Compile Remove="Models\XboxLive\XboxLiveRequest.cs"/>
    </ItemGroup>

</Project>
