<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:husk="https://github.com/d3ara1n/Huskui.Avalonia">
    <Design.PreviewWith>
        <Panel Background="White">
            <StackPanel Margin="24" Orientation="Horizontal">
                <husk:StepItem Header="Hello World" Content="This is a sample application using Huskui.Avalonia" />
            </StackPanel>
        </Panel>
    </Design.PreviewWith>

    <ControlTheme x:Key="{x:Type husk:StepItem}" TargetType="husk:StepItem">
        <Setter Property="MinWidth" Value="124" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <ControlTemplate>
                <Grid RowDefinitions="Auto,Auto,*" RowSpacing="8">
                    <Grid Grid.Row="0" ColumnDefinitions="*,Auto,*">
                        <Border Grid.Column="0" />
                        <Border Grid.Column="2" />
                    </Grid>
                    <ContentPresenter Grid.Row="1" Content="{TemplateBinding Header}"
                                      ContentTemplate="{TemplateBinding HeaderTemplate}"
                                      HorizontalContentAlignment="Center">
                        <ContentPresenter.Styles>
                            <Style Selector="ContentPresenter > TextBlock">
                                <Setter Property="FontSize" Value="{StaticResource LargeFontSize}" />
                                <Setter Property="FontWeight" Value="{StaticResource ControlStrongFontWeight}" />
                            </Style>
                        </ContentPresenter.Styles>
                    </ContentPresenter>
                    <ContentPresenter Grid.Row="2" Content="{TemplateBinding Content}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                      HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}">
                        <ContentPresenter.Styles>
                            <Style Selector="ContentPresenter > TextBlock">
                                <Setter Property="Foreground" Value="{StaticResource ControlSecondaryForegroundBrush}" />
                            </Style>
                        </ContentPresenter.Styles>
                    </ContentPresenter>
                </Grid>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>
